# Toggle Filter Implementation - List Layout Component

## Mụ<PERSON> tiêu
<PERSON>ể<PERSON> khai chức năng toggleFilter() trong list-layout component với animation slide up/down theo yêu cầu cụ thể.

## Yêu cầu đã triển khai

### 1. ✅ Function Implementation
- Tạo toggleFilter() method quản lý visibility state của `<app-field-filters>` component

### 2. ✅ Animation Sequence for SHOWING filters
- Render `<app-field-filters>` component trước (set ngIf to true)
- <PERSON><PERSON> <PERSON><PERSON> apply slide-down animation effect

### 3. ✅ Animation Sequence for HIDING filters  
- Apply slide-up animation effect trước
- <PERSON>u khi animation hoàn tất, remove component khỏi DOM (set ngIf to false)

### 4. ✅ Technical Implementation
- Sử dụng Angular Animations API với slide transitions (transform: translateY)
- Sử dụng *ngIf directive để conditionally render/remove `<app-field-filters>`
- Triển khai proper timing: render before show animation, remove after hide animation
- Thêm animation state management để coordinate ngIf với animation lifecycle

### 5. ✅ Animation Details
- Slide direction: vertical (up/down)
- Smooth transition với duration 300ms
- Sử dụng transform: translateY cho performance optimization

### 6. ✅ State Management
- Track filter visibility state trong component
- Proper cleanup sau khi hide animation completes
- Handle rapid toggle clicks gracefully

## Các file đã chỉnh sửa

### 1. list-layout.component.ts
- ✅ Import Angular Animations modules
- ✅ Thêm animation definitions trong component decorator
- ✅ Thêm state management signals:
  - `isFilterVisible = signal<boolean>(false)`
  - `isFilterRendered = signal<boolean>(false)`
  - `filterAnimationState = signal<'hidden' | 'visible'>('hidden')`
- ✅ Triển khai toggleFilter() logic với proper timing
- ✅ Thêm showFilter(), hideFilter(), onFilterAnimationDone() methods

### 2. list-layout.component.html
- ✅ Cập nhật template với ngIf và animation triggers
- ✅ Thêm animation binding [@slideFilter]
- ✅ Thêm animation event handler (@slideFilter.done)
- ✅ Cập nhật button filter với dynamic active class
- ✅ Thêm full-width class cho table-wrapper khi filter ẩn

### 3. list-layout.component.scss
- ✅ Thêm animation support CSS cho .field-filters
- ✅ Thêm transition cho .table-wrapper width
- ✅ Thêm .full-width class cho responsive layout

## Vấn đề hiện tại

### 🔍 Debug cần thiết
- Build thành công nhưng filter panel không xuất hiện khi click
- Không có lỗi trong console
- Animation có thể không hoạt động đúng
- Cần kiểm tra:
  1. Event binding có hoạt động không
  2. State signals có được update không
  3. Animation trigger có được gọi không

### Các bước tiếp theo
1. ✅ Thêm console.log để debug state changes
2. ✅ Kiểm tra xem click event có được trigger không
3. ✅ Verify animation state transitions
4. ✅ Test trên browser với dev tools

## Kết quả mong đợi
- Click vào filter button sẽ toggle filter panel với smooth slide animation
- Filter panel slide down khi show, slide up khi hide
- Table layout tự động adjust width khi filter ẩn/hiện
- Button filter hiển thị active state khi filter visible
