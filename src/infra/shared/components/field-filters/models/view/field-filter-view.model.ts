import { Field } from '@domain/entities/field.entity';

// Các operator cho từng loại field
export type TextFilterOperator =
  | 'is'
  | 'isnt'
  | 'contains'
  | 'doesnt_contain'
  | 'starts_with'
  | 'ends_with'
  | 'is_empty'
  | 'is_not_empty';

export type NumberFilterOperator =
  | 'equals'
  | 'not_equals'
  | 'less_than'
  | 'less_than_or_equal'
  | 'greater_than'
  | 'greater_than_or_equal'
  | 'between'
  | 'not_between'
  | 'is_empty'
  | 'is_not_empty';

export type DateFilterOperator =
  | 'age_in'
  | 'due_in'
  | 'previous'
  | 'next'
  | 'on'
  | 'before'
  | 'after'
  | 'between'
  | 'not_between'
  | 'today'
  | 'tomorrow'
  | 'till_yesterday'
  | 'starting_tomorrow'
  | 'yesterday'
  | 'this_week'
  | 'this_month'
  | 'previous_week'
  | 'previous_month'
  | 'this_year'
  | 'current_fy'
  | 'current_fq'
  | 'previous_year'
  | 'previous_fy'
  | 'previous_fq'
  | 'next_year'
  | 'next_fq'
  | 'is_empty'
  | 'is_not_empty';

export type PicklistFilterOperator =
  | 'is'
  | 'is_not'
  | 'is_empty'
  | 'is_not_empty';

export type CheckboxFilterOperator = 'is';

export type FilterOperator =
  | TextFilterOperator
  | NumberFilterOperator
  | DateFilterOperator
  | PicklistFilterOperator
  | CheckboxFilterOperator;

// Giá trị filter cho từng loại
export interface TextFilterValue {
  operator: TextFilterOperator;
  value?: string;
}

export interface NumberFilterValue {
  operator: NumberFilterOperator;
  value?: number;
  minValue?: number;
  maxValue?: number;
}

export interface DateFilterValue {
  operator: DateFilterOperator;
  value?: string; // ISO date string
  minValue?: string;
  maxValue?: string;
  timeValue?: number; // cho age_in, due_in, previous, next
  timeUnit?: 'days' | 'weeks' | 'months' | 'years';
}

export interface PicklistFilterValue {
  operator: PicklistFilterOperator;
  values?: string[];
}

export interface CheckboxFilterValue {
  operator: CheckboxFilterOperator;
  value: 'selected' | 'not_selected';
}

export type FilterValue =
  | TextFilterValue
  | NumberFilterValue
  | DateFilterValue
  | PicklistFilterValue
  | CheckboxFilterValue;

// Model chính cho field filter
export interface FieldFilter {
  field: Field;
  isActive: boolean;
  filterValue?: FilterValue;
}

// Model cho danh sách field filters
export interface FieldFiltersState {
  filters: FieldFilter[];
}

// Event khi filter thay đổi
export interface FilterChangeEvent {
  fieldId: number;
  isActive: boolean;
  filterValue?: FilterValue;
}

// Cấu hình operator cho từng loại field
export interface OperatorConfig {
  value: FilterOperator;
  labelKey: string;
  requiresInput: boolean;
  inputType?: 'text' | 'number' | 'date' | 'range' | 'picklist' | 'checkbox' | 'time-unit';
}

// Mapping operator theo field type
export const FIELD_OPERATORS: Record<string, OperatorConfig[]> = {
  text: [
    { value: 'contains', labelKey: 'FIELD_FILTERS.OPERATORS.CONTAINS', requiresInput: true, inputType: 'text' },
    { value: 'doesnt_contain', labelKey: 'FIELD_FILTERS.OPERATORS.DOESNT_CONTAIN', requiresInput: true, inputType: 'text' },
    { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'text' },
    { value: 'isnt', labelKey: 'FIELD_FILTERS.OPERATORS.ISNT', requiresInput: true, inputType: 'text' },
    { value: 'starts_with', labelKey: 'FIELD_FILTERS.OPERATORS.STARTS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'ends_with', labelKey: 'FIELD_FILTERS.OPERATORS.ENDS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  email: [
    { value: 'contains', labelKey: 'FIELD_FILTERS.OPERATORS.CONTAINS', requiresInput: true, inputType: 'text' },
    { value: 'doesnt_contain', labelKey: 'FIELD_FILTERS.OPERATORS.DOESNT_CONTAIN', requiresInput: true, inputType: 'text' },
    { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'text' },
    { value: 'isnt', labelKey: 'FIELD_FILTERS.OPERATORS.ISNT', requiresInput: true, inputType: 'text' },
    { value: 'starts_with', labelKey: 'FIELD_FILTERS.OPERATORS.STARTS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'ends_with', labelKey: 'FIELD_FILTERS.OPERATORS.ENDS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  phone: [
    { value: 'contains', labelKey: 'FIELD_FILTERS.OPERATORS.CONTAINS', requiresInput: true, inputType: 'text' },
    { value: 'doesnt_contain', labelKey: 'FIELD_FILTERS.OPERATORS.DOESNT_CONTAIN', requiresInput: true, inputType: 'text' },
    { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'text' },
    { value: 'isnt', labelKey: 'FIELD_FILTERS.OPERATORS.ISNT', requiresInput: true, inputType: 'text' },
    { value: 'starts_with', labelKey: 'FIELD_FILTERS.OPERATORS.STARTS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'ends_with', labelKey: 'FIELD_FILTERS.OPERATORS.ENDS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  url: [
    { value: 'contains', labelKey: 'FIELD_FILTERS.OPERATORS.CONTAINS', requiresInput: true, inputType: 'text' },
    { value: 'doesnt_contain', labelKey: 'FIELD_FILTERS.OPERATORS.DOESNT_CONTAIN', requiresInput: true, inputType: 'text' },
    { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'text' },
    { value: 'isnt', labelKey: 'FIELD_FILTERS.OPERATORS.ISNT', requiresInput: true, inputType: 'text' },
    { value: 'starts_with', labelKey: 'FIELD_FILTERS.OPERATORS.STARTS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'ends_with', labelKey: 'FIELD_FILTERS.OPERATORS.ENDS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  textarea: [
    { value: 'contains', labelKey: 'FIELD_FILTERS.OPERATORS.CONTAINS', requiresInput: true, inputType: 'text' },
    { value: 'doesnt_contain', labelKey: 'FIELD_FILTERS.OPERATORS.DOESNT_CONTAIN', requiresInput: true, inputType: 'text' },
    { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'text' },
    { value: 'isnt', labelKey: 'FIELD_FILTERS.OPERATORS.ISNT', requiresInput: true, inputType: 'text' },
    { value: 'starts_with', labelKey: 'FIELD_FILTERS.OPERATORS.STARTS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'ends_with', labelKey: 'FIELD_FILTERS.OPERATORS.ENDS_WITH', requiresInput: true, inputType: 'text' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  number: [
    { value: 'equals', labelKey: 'FIELD_FILTERS.OPERATORS.EQUALS', requiresInput: true, inputType: 'number' },
    { value: 'not_equals', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_EQUALS', requiresInput: true, inputType: 'number' },
    { value: 'less_than', labelKey: 'FIELD_FILTERS.OPERATORS.LESS_THAN', requiresInput: true, inputType: 'number' },
    { value: 'less_than_or_equal', labelKey: 'FIELD_FILTERS.OPERATORS.LESS_THAN_OR_EQUAL', requiresInput: true, inputType: 'number' },
    { value: 'greater_than', labelKey: 'FIELD_FILTERS.OPERATORS.GREATER_THAN', requiresInput: true, inputType: 'number' },
    { value: 'greater_than_or_equal', labelKey: 'FIELD_FILTERS.OPERATORS.GREATER_THAN_OR_EQUAL', requiresInput: true, inputType: 'number' },
    { value: 'between', labelKey: 'FIELD_FILTERS.OPERATORS.BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'not_between', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  decimal: [
    { value: 'equals', labelKey: 'FIELD_FILTERS.OPERATORS.EQUALS', requiresInput: true, inputType: 'number' },
    { value: 'not_equals', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_EQUALS', requiresInput: true, inputType: 'number' },
    { value: 'less_than', labelKey: 'FIELD_FILTERS.OPERATORS.LESS_THAN', requiresInput: true, inputType: 'number' },
    { value: 'less_than_or_equal', labelKey: 'FIELD_FILTERS.OPERATORS.LESS_THAN_OR_EQUAL', requiresInput: true, inputType: 'number' },
    { value: 'greater_than', labelKey: 'FIELD_FILTERS.OPERATORS.GREATER_THAN', requiresInput: true, inputType: 'number' },
    { value: 'greater_than_or_equal', labelKey: 'FIELD_FILTERS.OPERATORS.GREATER_THAN_OR_EQUAL', requiresInput: true, inputType: 'number' },
    { value: 'between', labelKey: 'FIELD_FILTERS.OPERATORS.BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'not_between', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  currency: [
    { value: 'equals', labelKey: 'FIELD_FILTERS.OPERATORS.EQUALS', requiresInput: true, inputType: 'number' },
    { value: 'not_equals', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_EQUALS', requiresInput: true, inputType: 'number' },
    { value: 'less_than', labelKey: 'FIELD_FILTERS.OPERATORS.LESS_THAN', requiresInput: true, inputType: 'number' },
    { value: 'less_than_or_equal', labelKey: 'FIELD_FILTERS.OPERATORS.LESS_THAN_OR_EQUAL', requiresInput: true, inputType: 'number' },
    { value: 'greater_than', labelKey: 'FIELD_FILTERS.OPERATORS.GREATER_THAN', requiresInput: true, inputType: 'number' },
    { value: 'greater_than_or_equal', labelKey: 'FIELD_FILTERS.OPERATORS.GREATER_THAN_OR_EQUAL', requiresInput: true, inputType: 'number' },
    { value: 'between', labelKey: 'FIELD_FILTERS.OPERATORS.BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'not_between', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  percent: [
    { value: 'equals', labelKey: 'FIELD_FILTERS.OPERATORS.EQUALS', requiresInput: true, inputType: 'number' },
    { value: 'not_equals', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_EQUALS', requiresInput: true, inputType: 'number' },
    { value: 'less_than', labelKey: 'FIELD_FILTERS.OPERATORS.LESS_THAN', requiresInput: true, inputType: 'number' },
    { value: 'less_than_or_equal', labelKey: 'FIELD_FILTERS.OPERATORS.LESS_THAN_OR_EQUAL', requiresInput: true, inputType: 'number' },
    { value: 'greater_than', labelKey: 'FIELD_FILTERS.OPERATORS.GREATER_THAN', requiresInput: true, inputType: 'number' },
    { value: 'greater_than_or_equal', labelKey: 'FIELD_FILTERS.OPERATORS.GREATER_THAN_OR_EQUAL', requiresInput: true, inputType: 'number' },
    { value: 'between', labelKey: 'FIELD_FILTERS.OPERATORS.BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'not_between', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  date: [
    { value: 'age_in', labelKey: 'FIELD_FILTERS.OPERATORS.AGE_IN', requiresInput: true, inputType: 'time-unit' },
    { value: 'due_in', labelKey: 'FIELD_FILTERS.OPERATORS.DUE_IN', requiresInput: true, inputType: 'time-unit' },
    { value: 'previous', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS', requiresInput: true, inputType: 'time-unit' },
    { value: 'next', labelKey: 'FIELD_FILTERS.OPERATORS.NEXT', requiresInput: true, inputType: 'time-unit' },
    { value: 'on', labelKey: 'FIELD_FILTERS.OPERATORS.ON', requiresInput: true, inputType: 'date' },
    { value: 'before', labelKey: 'FIELD_FILTERS.OPERATORS.BEFORE', requiresInput: true, inputType: 'date' },
    { value: 'after', labelKey: 'FIELD_FILTERS.OPERATORS.AFTER', requiresInput: true, inputType: 'date' },
    { value: 'between', labelKey: 'FIELD_FILTERS.OPERATORS.BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'not_between', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'today', labelKey: 'FIELD_FILTERS.OPERATORS.TODAY', requiresInput: false },
    { value: 'tomorrow', labelKey: 'FIELD_FILTERS.OPERATORS.TOMORROW', requiresInput: false },
    { value: 'till_yesterday', labelKey: 'FIELD_FILTERS.OPERATORS.TILL_YESTERDAY', requiresInput: false },
    { value: 'starting_tomorrow', labelKey: 'FIELD_FILTERS.OPERATORS.STARTING_TOMORROW', requiresInput: false },
    { value: 'yesterday', labelKey: 'FIELD_FILTERS.OPERATORS.YESTERDAY', requiresInput: false },
    { value: 'this_week', labelKey: 'FIELD_FILTERS.OPERATORS.THIS_WEEK', requiresInput: false },
    { value: 'this_month', labelKey: 'FIELD_FILTERS.OPERATORS.THIS_MONTH', requiresInput: false },
    { value: 'previous_week', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_WEEK', requiresInput: false },
    { value: 'previous_month', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_MONTH', requiresInput: false },
    { value: 'this_year', labelKey: 'FIELD_FILTERS.OPERATORS.THIS_YEAR', requiresInput: false },
    { value: 'current_fy', labelKey: 'FIELD_FILTERS.OPERATORS.CURRENT_FY', requiresInput: false },
    { value: 'current_fq', labelKey: 'FIELD_FILTERS.OPERATORS.CURRENT_FQ', requiresInput: false },
    { value: 'previous_year', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_YEAR', requiresInput: false },
    { value: 'previous_fy', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_FY', requiresInput: false },
    { value: 'previous_fq', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_FQ', requiresInput: false },
    { value: 'next_year', labelKey: 'FIELD_FILTERS.OPERATORS.NEXT_YEAR', requiresInput: false },
    { value: 'next_fq', labelKey: 'FIELD_FILTERS.OPERATORS.NEXT_FQ', requiresInput: false },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  datetime: [
    { value: 'age_in', labelKey: 'FIELD_FILTERS.OPERATORS.AGE_IN', requiresInput: true, inputType: 'time-unit' },
    { value: 'due_in', labelKey: 'FIELD_FILTERS.OPERATORS.DUE_IN', requiresInput: true, inputType: 'time-unit' },
    { value: 'previous', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS', requiresInput: true, inputType: 'time-unit' },
    { value: 'next', labelKey: 'FIELD_FILTERS.OPERATORS.NEXT', requiresInput: true, inputType: 'time-unit' },
    { value: 'on', labelKey: 'FIELD_FILTERS.OPERATORS.ON', requiresInput: true, inputType: 'date' },
    { value: 'before', labelKey: 'FIELD_FILTERS.OPERATORS.BEFORE', requiresInput: true, inputType: 'date' },
    { value: 'after', labelKey: 'FIELD_FILTERS.OPERATORS.AFTER', requiresInput: true, inputType: 'date' },
    { value: 'between', labelKey: 'FIELD_FILTERS.OPERATORS.BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'not_between', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_BETWEEN', requiresInput: true, inputType: 'range' },
    { value: 'today', labelKey: 'FIELD_FILTERS.OPERATORS.TODAY', requiresInput: false },
    { value: 'tomorrow', labelKey: 'FIELD_FILTERS.OPERATORS.TOMORROW', requiresInput: false },
    { value: 'till_yesterday', labelKey: 'FIELD_FILTERS.OPERATORS.TILL_YESTERDAY', requiresInput: false },
    { value: 'starting_tomorrow', labelKey: 'FIELD_FILTERS.OPERATORS.STARTING_TOMORROW', requiresInput: false },
    { value: 'yesterday', labelKey: 'FIELD_FILTERS.OPERATORS.YESTERDAY', requiresInput: false },
    { value: 'this_week', labelKey: 'FIELD_FILTERS.OPERATORS.THIS_WEEK', requiresInput: false },
    { value: 'this_month', labelKey: 'FIELD_FILTERS.OPERATORS.THIS_MONTH', requiresInput: false },
    { value: 'previous_week', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_WEEK', requiresInput: false },
    { value: 'previous_month', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_MONTH', requiresInput: false },
    { value: 'this_year', labelKey: 'FIELD_FILTERS.OPERATORS.THIS_YEAR', requiresInput: false },
    { value: 'current_fy', labelKey: 'FIELD_FILTERS.OPERATORS.CURRENT_FY', requiresInput: false },
    { value: 'current_fq', labelKey: 'FIELD_FILTERS.OPERATORS.CURRENT_FQ', requiresInput: false },
    { value: 'previous_year', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_YEAR', requiresInput: false },
    { value: 'previous_fy', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_FY', requiresInput: false },
    { value: 'previous_fq', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_FQ', requiresInput: false },
    { value: 'next_year', labelKey: 'FIELD_FILTERS.OPERATORS.NEXT_YEAR', requiresInput: false },
    { value: 'next_fq', labelKey: 'FIELD_FILTERS.OPERATORS.NEXT_FQ', requiresInput: false },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  picklist: [
    { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'picklist' },
    { value: 'is_not', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT', requiresInput: true, inputType: 'picklist' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  'multi-picklist': [
    { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'picklist' },
    { value: 'is_not', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT', requiresInput: true, inputType: 'picklist' },
    { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
    { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
  ],
  checkbox: [
    { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'checkbox' }
  ]
};
