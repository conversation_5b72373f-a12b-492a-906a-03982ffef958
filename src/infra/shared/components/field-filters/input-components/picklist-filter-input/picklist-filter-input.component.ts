import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  On<PERSON><PERSON>roy,
  ChangeDetectionStrategy,
  signal,
  computed,
  effect,
  model
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// Angular Material imports
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

import { Field } from '@domain/entities/field.entity';
import {
  BaseFilterInput,
  PicklistFilterValue
} from '../../models/view/field-filters.model';
import { COMMA, ENTER } from '@angular/cdk/keycodes';

/**
 * Specialized component cho picklist-based filter inputs
 * Handles: picklist, multi-picklist field types
 * Operators: is, is_not, is_empty, is_not_empty
 */
@Component({
  selector: 'app-picklist-filter-input',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatFormFieldModule,
    MatInputModule,
    MatChipsModule,
    MatAutocompleteModule,
    MatIconModule,
    FormsModule
  ],
  templateUrl: './picklist-filter-input.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PicklistFilterInputComponent implements BaseFilterInput, OnInit, OnDestroy {
  @Input() field!: Field;
  @Input() operator!: string;
  @Input() value?: string[];
  @Input() disabled = false;

  @Output() valueChange = new EventEmitter<string[]>();
  @Output() validationChange = new EventEmitter<boolean>();

  // Signals cho reactive state management
  readonly selectedValues = signal<string[]>([]);
  readonly inputValue = model('');
  readonly isValid = signal<boolean>(true);
  readonly separatorKeysCodes: number[] = [ENTER, COMMA];

  // Computed properties
  readonly requiresInput = computed(() => {
    return !['is_empty', 'is_not_empty'].includes(this.operator);
  });

  readonly availableOptions = computed(() => {
    if (!this.field?.constraints) return [];

    // Lấy options từ field constraints
    const constraints = this.field.constraints as any;
    return constraints.picklistValues || constraints.options || this.getDefaultOptions();
  });

  readonly filteredOptions = computed(() => {
    const inputValue = this.inputValue().toLowerCase();
    const selectedValues = this.selectedValues();

    return this.availableOptions()
      .filter((option: string) =>
        option.toLowerCase().includes(inputValue) &&
        !selectedValues.includes(option)
      );
  });

  readonly isMultiSelect = computed(() => {
    return this.field?.type === 'multi-picklist' || this.operator === 'is';
  });

  readonly placeholder = computed(() => {
    if (this.selectedValues().length === 0) {
      return 'FIELD_FILTERS.PLACEHOLDERS.SELECT_VALUES';
    }
    return '';
  });

  constructor() {
    // Effect để sync với external value
    effect(() => {
      if (this.value && Array.isArray(this.value)) {
        this.selectedValues.set([...this.value]);
      }
    });

    // Effect để emit changes
    effect(() => {
      const valid = this.validate();
      this.isValid.set(valid);

      const currentValues = this.selectedValues();
      this.valueChange.emit([...currentValues]);
      this.validationChange.emit(valid);
    });
  }

  ngOnInit(): void {
    // Initialize với value nếu có
    if (this.value && Array.isArray(this.value)) {
      this.selectedValues.set([...this.value]);
    }
  }

  ngOnDestroy(): void {
    // Cleanup nếu cần
  }

  /**
   * Handle chip selection từ autocomplete
   */
  onChipSelected(event: MatAutocompleteSelectedEvent): void {
    console.log('onChipSelected');

    const value = event.option.viewValue;
    const currentValues = this.selectedValues();

    if (!currentValues.includes(value)) {
      if (this.isMultiSelect()) {
        this.selectedValues.set([...currentValues, value]);
      } else {
        // Single select: replace existing value
        this.selectedValues.set([value]);
      }
    }

    this.inputValue.set('');
    event.option.deselect();
    console.log(this.inputValue());
  }

  /**
   * Handle chip removal
   */
  onChipRemoved(chipToRemove: string): void {
    const currentValues = this.selectedValues();
    this.selectedValues.set(currentValues.filter(value => value !== chipToRemove));
  }

  /**
   * Handle input change cho autocomplete
   */
  onInputChange(event: Event): void {
    console.log('onInputChange');

    const target = event.target as HTMLInputElement;
    this.inputValue.set(target.value);
  }

  /**
   * Handle chip input event (khi user nhấn Enter)
   */
  onChipInputEvent(event: MatChipInputEvent): void {
    console.log('onChipInputEvent');

    const value = event.value.trim();

    if (value && this.availableOptions().includes(value)) {
      const currentValues = this.selectedValues();

      if (!currentValues.includes(value)) {
        if (this.isMultiSelect()) {
          this.selectedValues.set([...currentValues, value]);
        } else {
          this.selectedValues.set([value]);
        }
      }
    }

    // Clear input
    event.chipInput!.clear();
    this.inputValue.set('');
  }

  /**
   * Validate current selection
   */
  validate(): boolean {
    if (!this.requiresInput()) {
      return true;
    }

    const selectedValues = this.selectedValues();

    // At least one value required cho operators cần input
    return selectedValues.length > 0;
  }

  /**
   * Reset selection
   */
  reset(): void {
    this.selectedValues.set([]);
    this.inputValue.set('');
  }

  /**
   * Get default options cho testing
   */
  private getDefaultOptions(): string[] {
    if (!this.field) return [];

    const fieldLabel = this.field.label.toLowerCase();
    if (fieldLabel.includes('status')) {
      return ['Active', 'Inactive', 'Pending', 'Completed', 'Cancelled'];
    }
    if (fieldLabel.includes('skill')) {
      return ['JavaScript', 'TypeScript', 'Angular', 'React', 'Vue', 'Node.js', 'Python', 'Java'];
    }
    if (fieldLabel.includes('category')) {
      return ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports'];
    }

    return ['Option 1', 'Option 2', 'Option 3', 'Option 4', 'Option 5'];
  }

  /**
   * Get current filter value trong format chuẩn
   */
  getCurrentFilterValue(): PicklistFilterValue {
    return {
      operator: this.operator,
      values: [...this.selectedValues()]
    };
  }
}
