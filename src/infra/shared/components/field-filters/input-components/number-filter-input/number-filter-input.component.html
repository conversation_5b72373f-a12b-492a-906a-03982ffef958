<!-- Number Filter Input Component -->
<div class="number-filter-input" *ngIf="requiresInput()">

  <!-- Single Value Input (equals, not_equals, less_than, greater_than, etc.) -->
  <div *ngIf="!isRangeOperator(); else rangeInput" class="single-value-input">
    <mat-form-field appearance="outline" class="w-100">
      <input
        matInput
        type="number"
        [step]="inputStep()"
        [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.ENTER_NUMBER' | translate"
        [value]="singleValue() || ''"
        [disabled]="disabled"
        (input)="onSingleValueChange($event)"
        class="number-input">

      <!-- Currency/Percent suffix -->
      <span matTextSuffix *ngIf="currencySymbol() || percentSymbol()">
        {{ currencySymbol() }}{{ percentSymbol() }}
      </span>

      <!-- Error message -->
      <mat-error *ngIf="!isValid()">
        {{ 'FIELD_FILTERS.VALIDATION.REQUIRED_NUMBER' | translate }}
      </mat-error>
    </mat-form-field>
  </div>

  <!-- Range Input (between, not_between) -->
  <ng-template #rangeInput>
    <div class="range-input">
      <div class="row g-2">
        <!-- Min Value -->
        <div class="col-6">
          <mat-form-field appearance="outline" class="w-100">
            <input
              matInput
              type="number"
              [step]="inputStep()"
              [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.MIN_VALUE' | translate"
              [value]="minValue() || ''"
              [disabled]="disabled"
              (input)="onMinValueChange($event)"
              class="number-input">

            <span matTextSuffix *ngIf="currencySymbol() || percentSymbol()">
              {{ currencySymbol() }}{{ percentSymbol() }}
            </span>
          </mat-form-field>
        </div>

        <!-- Max Value -->
        <div class="col-6">
          <mat-form-field appearance="outline" class="w-100">
            <input
              matInput
              type="number"
              [step]="inputStep()"
              [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.MAX_VALUE' | translate"
              [value]="maxValue() || ''"
              [disabled]="disabled"
              (input)="onMaxValueChange($event)"
              class="number-input">

            <span matTextSuffix *ngIf="currencySymbol() || percentSymbol()">
              {{ currencySymbol() }}{{ percentSymbol() }}
            </span>
          </mat-form-field>
        </div>
      </div>

      <!-- Range validation error -->
      <div class="range-error" *ngIf="!isValid()">
        <small class="text-danger">
          <span *ngIf="minValue() === null || maxValue() === null">
            {{ 'FIELD_FILTERS.VALIDATION.BOTH_VALUES_REQUIRED' | translate }}
          </span>
          <span *ngIf="minValue() !== null && maxValue() !== null && minValue()! > maxValue()!">
            {{ 'FIELD_FILTERS.VALIDATION.MIN_LESS_THAN_MAX' | translate }}
          </span>
        </small>
      </div>
    </div>
  </ng-template>
</div>

<!-- No input required message cho is_empty/is_not_empty operators -->
<div class="no-input-message" *ngIf="!requiresInput()">
  <small class="text-muted">
    {{ 'FIELD_FILTERS.MESSAGES.NO_INPUT_REQUIRED' | translate }}
  </small>
</div>
