import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  On<PERSON><PERSON>roy,
  ChangeDetectionStrategy,
  signal,
  computed,
  effect
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// Angular Material imports
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectChange } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';

import { Field } from '@domain/entities/field.entity';
import {
  BaseFilterInput,
  DateFilterValue,
  TimeUnitOption
} from '../../models/view/field-filters.model';

/**
 * Specialized component cho date-based filter inputs
 * Handles: date, datetime field types
 * Operators: age_in, due_in, previous, next, on, before, after, between, not_between, today, yesterday, etc.
 */
@Component({
  selector: 'app-date-filter-input',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule
  ],
  templateUrl: './date-filter-input.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DateFilterInputComponent implements BaseFilterInput, OnInit, OnDestroy {
  @Input() field!: Field;
  @Input() operator!: string;
  @Input() value?: DateFilterValue;
  @Input() disabled = false;

  @Output() valueChange = new EventEmitter<DateFilterValue>();
  @Output() validationChange = new EventEmitter<boolean>();

  // Signals cho reactive state management
  readonly singleDate = signal<Date | null>(null);
  readonly minDate = signal<Date | null>(null);
  readonly maxDate = signal<Date | null>(null);
  readonly timeValue = signal<number>(1);
  readonly timeUnit = signal<'days' | 'weeks' | 'months' | 'years'>('days');
  readonly isValid = signal<boolean>(true);

  // Time unit options
  readonly timeUnits: TimeUnitOption[] = [
    { value: 'days', labelKey: 'FIELD_FILTERS.TIME_UNITS.DAYS' },
    { value: 'weeks', labelKey: 'FIELD_FILTERS.TIME_UNITS.WEEKS' },
    { value: 'months', labelKey: 'FIELD_FILTERS.TIME_UNITS.MONTHS' },
    { value: 'years', labelKey: 'FIELD_FILTERS.TIME_UNITS.YEARS' }
  ];

  // Computed properties
  readonly requiresInput = computed(() => {
    const noInputOperators = [
      'today', 'tomorrow', 'till_yesterday', 'starting_tomorrow', 'yesterday',
      'this_week', 'this_month', 'previous_week', 'previous_month',
      'this_year', 'current_fy', 'current_fq', 'previous_year',
      'previous_fy', 'previous_fq', 'next_year', 'next_fq',
      'is_empty', 'is_not_empty'
    ];
    return !noInputOperators.includes(this.operator);
  });

  readonly isTimeUnitOperator = computed(() => {
    return ['age_in', 'due_in', 'previous', 'next'].includes(this.operator);
  });

  readonly isSingleDateOperator = computed(() => {
    return ['on', 'before', 'after'].includes(this.operator);
  });

  readonly isRangeOperator = computed(() => {
    return ['between', 'not_between'].includes(this.operator);
  });

  constructor() {
    // Effect để sync với external value
    effect(() => {
      if (this.value) {
        if (this.isTimeUnitOperator()) {
          this.timeValue.set(this.value.timeValue || 1);
          this.timeUnit.set(this.value.timeUnit || 'days');
        } else if (this.isSingleDateOperator()) {
          this.singleDate.set(this.value.value ? new Date(this.value.value) : null);
        } else if (this.isRangeOperator()) {
          this.minDate.set(this.value.minValue ? new Date(this.value.minValue) : null);
          this.maxDate.set(this.value.maxValue ? new Date(this.value.maxValue) : null);
        }
      }
    });

    // Effect để emit changes
    effect(() => {
      const valid = this.validate();
      this.isValid.set(valid);

      const currentValue = this.getCurrentFilterValue();
      this.valueChange.emit(currentValue);
      this.validationChange.emit(valid);
    });
  }

  ngOnInit(): void {
    // Initialize với default values
    if (!this.value && this.isTimeUnitOperator()) {
      this.timeValue.set(1);
      this.timeUnit.set('days');
    }
  }

  ngOnDestroy(): void {
    // Cleanup nếu cần
  }

  /**
   * Handle single date change
   */
  onSingleDateChange(event: MatDatepickerInputEvent<Date>): void {
    this.singleDate.set(event.value);
  }

  /**
   * Handle min date change cho range operators
   */
  onMinDateChange(event: MatDatepickerInputEvent<Date>): void {
    this.minDate.set(event.value);
  }

  /**
   * Handle max date change cho range operators
   */
  onMaxDateChange(event: MatDatepickerInputEvent<Date>): void {
    this.maxDate.set(event.value);
  }

  /**
   * Handle time value change
   */
  onTimeValueChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = parseInt(target.value) || 1;
    this.timeValue.set(Math.max(1, value)); // Minimum 1
  }

  /**
   * Handle time unit change
   */
  onTimeUnitChange(event: MatSelectChange): void {
    this.timeUnit.set(event.value);
  }

  /**
   * Validate current input values
   */
  validate(): boolean {
    if (!this.requiresInput()) {
      return true;
    }

    if (this.isTimeUnitOperator()) {
      return this.timeValue() > 0;
    }

    if (this.isSingleDateOperator()) {
      return this.singleDate() !== null;
    }

    if (this.isRangeOperator()) {
      const min = this.minDate();
      const max = this.maxDate();

      if (!min || !max) {
        return false;
      }

      return min <= max;
    }

    return true;
  }

  /**
   * Reset input values
   */
  reset(): void {
    this.singleDate.set(null);
    this.minDate.set(null);
    this.maxDate.set(null);
    this.timeValue.set(1);
    this.timeUnit.set('days');
  }

  /**
   * Get current filter value trong format chuẩn
   */
  getCurrentFilterValue(): DateFilterValue {
    const baseValue: DateFilterValue = {
      operator: this.operator
    };

    if (this.isTimeUnitOperator()) {
      baseValue.timeValue = this.timeValue();
      baseValue.timeUnit = this.timeUnit();
    } else if (this.isSingleDateOperator()) {
      const date = this.singleDate();
      baseValue.value = date ? date.toISOString().split('T')[0] : '';
    } else if (this.isRangeOperator()) {
      const min = this.minDate();
      const max = this.maxDate();
      baseValue.minValue = min ? min.toISOString().split('T')[0] : '';
      baseValue.maxValue = max ? max.toISOString().split('T')[0] : '';
    }

    return baseValue;
  }
}
