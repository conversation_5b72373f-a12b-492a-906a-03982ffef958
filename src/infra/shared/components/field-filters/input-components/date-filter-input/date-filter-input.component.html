<!-- Date Filter Input Component -->
<div class="date-filter-input" *ngIf="requiresInput()">

  <!-- Time Unit Input (age_in, due_in, previous, next) -->
  <div *ngIf="isTimeUnitOperator(); else datePickerInput" class="time-unit-input">
    <div class="row g-2">
      <!-- Time Value -->
      <div class="col-6">
        <mat-form-field appearance="outline" class="w-100">
          <input
            matInput
            type="number"
            min="1"
            step="1"
            [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.ENTER_NUMBER' | translate"
            [value]="timeValue()"
            [disabled]="disabled"
            (input)="onTimeValueChange($event)"
            class="time-value-input">

          <mat-error *ngIf="timeValue() <= 0">
            {{ 'FIELD_FILTERS.VALIDATION.POSITIVE_NUMBER_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Time Unit -->
      <div class="col-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-select
            [value]="timeUnit()"
            [disabled]="disabled"
            (selectionChange)="onTimeUnitChange($event)">
            <mat-option
              *ngFor="let unit of timeUnits"
              [value]="unit.value">
              {{ unit.labelKey | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>

  <!-- Date Picker Input Template -->
  <ng-template #datePickerInput>

    <!-- Single Date Input (on, before, after) -->
    <div *ngIf="isSingleDateOperator(); else rangeDateInput" class="single-date-input">
      <mat-form-field appearance="outline" class="w-100">
        <input
          matInput
          [matDatepicker]="singleDatePicker"
          [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.SELECT_DATE' | translate"
          [value]="singleDate()"
          [disabled]="disabled"
          (dateInput)="onSingleDateChange($event)"
          readonly>

        <mat-datepicker-toggle matIconSuffix [for]="singleDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #singleDatePicker></mat-datepicker>

        <mat-error *ngIf="!isValid()">
          {{ 'FIELD_FILTERS.VALIDATION.DATE_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Range Date Input (between, not_between) -->
    <ng-template #rangeDateInput>
      <div class="range-date-input" *ngIf="isRangeOperator()">
        <div class="row g-2">
          <!-- Start Date -->
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <input
              matInput
              [matDatepicker]="startDatePicker"
              [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.START_DATE' | translate"
              [value]="minDate()"
              [disabled]="disabled"
              (dateInput)="onMinDateChange($event)"
              readonly>

            <mat-datepicker-toggle matIconSuffix [for]="startDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #startDatePicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-100 mb-2">
            <input
              matInput
              [matDatepicker]="endDatePicker"
              [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.END_DATE' | translate"
              [value]="maxDate()"
              [disabled]="disabled"
              (dateInput)="onMaxDateChange($event)"
              readonly>

            <mat-datepicker-toggle matIconSuffix [for]="endDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #endDatePicker></mat-datepicker>
          </mat-form-field>
        </div>

        <!-- Range validation error -->
        <div class="range-error" *ngIf="!isValid()">
          <small class="text-danger">
            <span *ngIf="minDate() === null || maxDate() === null">
              {{ 'FIELD_FILTERS.VALIDATION.BOTH_DATES_REQUIRED' | translate }}
            </span>
            <span *ngIf="minDate() !== null && maxDate() !== null && minDate()! > maxDate()!">
              {{ 'FIELD_FILTERS.VALIDATION.START_DATE_BEFORE_END_DATE' | translate }}
            </span>
          </small>
        </div>
      </div>
    </ng-template>
  </ng-template>
</div>

<!-- No input required message cho preset operators -->
<div class="no-input-message" *ngIf="!requiresInput()">
  <small class="text-muted">
    {{ 'FIELD_FILTERS.MESSAGES.NO_INPUT_REQUIRED' | translate }}
  </small>
</div>
