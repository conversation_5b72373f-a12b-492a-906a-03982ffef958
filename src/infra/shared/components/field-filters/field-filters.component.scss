$headerHeight: 80;
$footerHeight: 70;

app-field-filters,
app-text-filter-input,
app-number-filter-input,
app-checkbox-filter-input,
app-picklist-filter-input,
app-date-filter-input  {
  display: block;
  width: 100%;
  height: 100%;
}

.filters-header {
  height: $headerHeight+px;
}

.field-filters-container {
  // Responsive design
  @media (max-width: 768px) {
    padding: 0.75rem;
  }

  h5 {
    color: #495057;
    font-weight: 600;
  }

  .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;

    &:hover {
      background-color: #6c757d;
      border-color: #6c757d;
      color: #fff;
    }
  }
}

.filters-list {
  height: calc(100% - #{($headerHeight + $footerHeight)+px});
  overflow-y: auto;
  scrollbar-width: thin;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}



.field-filters {
  .mat-mdc-form-field {
    height: 34px;
    width: 100%;

    .mdc-text-field--outlined {
      padding: 0;
    }

    // Container chính của form field
    .mat-mdc-form-field-flex {
      height: 34px;
      align-items: center;
      padding: 0 12px;
      position: relative;

      // Đảm bảo không có margin/padding thừa
      margin: 0;
      box-sizing: border-box;
    }

    // Container chứa input
    .mat-mdc-form-field-infix {
      min-height: 34px;
      height: 34px;
      padding: 0;
      border-top: none;
      display: flex;
      align-items: center;
      position: relative;

      // Loại bỏ padding top để tránh label space
      padding-top: 0 !important;
      margin-top: 0 !important;
    }

    // Input element chính
    .mat-mdc-input-element {
      height: 32px; // Nhỏ hơn 1 chút để có padding
      line-height: 32px;
      padding: 0 8px;
      font-size: 14px;
      font-weight: 400;
      color: #495057;
      background: transparent;
      border: none;
      outline: none;
      width: 100%;
      box-sizing: border-box;
      letter-spacing: -0.05px;

      &::placeholder {
        color: #6c757d;
        opacity: 0.8;
        font-weight: 400;
      }

      // Focus state cho input
      &:focus {
        outline: none;
        box-shadow: none;
      }
    }

    // Outline border styling
    .mat-mdc-form-field-outline {
      .mat-mdc-form-field-outline-start,
      .mat-mdc-form-field-outline-gap,
      .mat-mdc-form-field-outline-end {
        border-width: 1px;
        border-color: #ced4da;
        border-style: solid;
      }

      // Đảm bảo outline gap không hiển thị (vì không có label)
      .mat-mdc-form-field-outline-gap {
        border-top: 1px solid #ced4da;
      }
    }

    // Hover state
    &:hover:not(.mat-focused):not(.mat-form-field-disabled) {
      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-gap,
        .mat-mdc-form-field-outline-end {
          border-color: #adb5bd;
        }
      }
    }

    // Focus state
    &.mat-focused {
      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-gap,
        .mat-mdc-form-field-outline-end {
          border-color: #007bff;
          border-width: 2px;
        }
      }
    }

    // Error state
    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-gap,
        .mat-mdc-form-field-outline-end {
          border-color: #dc3545;
          border-width: 1px;
        }
      }

      .mat-mdc-input-element {
        color: #dc3545;
      }
    }

    // Disabled state
    &.mat-form-field-disabled {
      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-gap,
        .mat-mdc-form-field-outline-end {
          border-color: #e9ecef;
          border-style: dashed;
        }
      }

      .mat-mdc-input-element {
        color: #6c757d;
        background-color: #f8f9fa;
        cursor: not-allowed;
      }
    }

    // Loại bỏ subscript wrapper để tiết kiệm không gian
    .mat-mdc-form-field-subscript-wrapper {
      display: none !important;
      height: 0 !important;
      overflow: hidden !important;
    }
  }

  // Điều chỉnh prefix và suffix icons
  .mat-mdc-form-field-icon-prefix,
  .mat-mdc-form-field-icon-suffix {
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;

    .mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: #6c757d;
      line-height: 18px;
    }

    // Button trong suffix/prefix
    .mat-icon-button {
      width: 28px;
      height: 28px;
      line-height: 28px;

      .mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }
}

.filter-item {
  .field-header {
    mat-checkbox {
      .mdc-form-field {
        letter-spacing: -0.2px;
        align-items: center;
      }
    }
  }

  .filter-options-panel {
    position: relative;
    margin-right: 5px;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px 7px;

    &:after,
    &:before {
      bottom: 100%;
      left: 60px;
      border: solid transparent;
      content: "";
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
    }

    &:after {
      border-color: rgba(255, 255, 255, 0);
      border-bottom-color: #fff;
      border-width: 10px;
      margin-left: -10px;
    }
    &:before {
      border-color: rgba(221, 221, 221, 0);
      border-bottom-color: #ddd;
      border-width: 11px;
      margin-left: -11px;
    }
  }

  .mat-mdc-form-field {
    height: 26px;

    .mat-mdc-form-field-flex,
    .mat-mdc-form-field-infix {
      height: 26px;
    }
    .mat-mdc-input-element {
      height: 24px; // Nhỏ hơn 1 chút để có padding
      line-height: 24px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #000 !important;
    }

    .mat-mdc-select,
    .mat-mdc-option {
      font-size: 14px;
      font-weight: 500;
    }
    .mat-mdc-form-field-text-suffix {
      font-size: 13px;
    }
  }

  .date-filter-input {
    .mat-mdc-form-field .mat-mdc-form-field-flex,
    .mat-mdc-form-field .mat-mdc-form-field-infix {
      height: 34px;
    }
  }
  .checkbox-filter-input {
    .option-content.selected {
      .mat-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }
    }
  }
  .picklist-filter-input {
    mat-chip-grid {
      .mat-mdc-standard-chip {
        height: 22px;

        .mdc-evolution-chip__text-label {
            font-size: 13px;
          }
        }
         .mdc-evolution-chip__action--primary{
            &::before {
              border-radius: 4px;
            }
         }
      }
      .mat-mdc-chip-remove .mat-icon {
        width: 14px;
        height: 14px;
        font-size: 14px;
      }
  }

  .range-error {
    margin: 3px 0 0 6px;

    small {
      font-size: 0.75rem;
    }
  }

  .value-explanation {
    margin: 3px 0 0 6px;


    small {
      font-size: 0.7rem;
    }
  }

  .no-input-message {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    text-align: center;

    small {
      font-size: 0.75rem;
      color: #6c757d;
      font-style: italic;
    }
  }
}

.active-filters-summary {
  small {
    display: flex;
    align-items: center;

    i {
      color: #007bff;
    }
  }
}

// Alert styles
.alert {
  border: none;
  border-radius: 0.375rem;

  &.alert-info {
    background-color: #e7f3ff;
    color: #0c5460;

    i {
      color: #0dcaf0;
    }
  }
}

// Filter Actions Buttons
.filter-actions {
  height: $footerHeight+px;

  .btn {
    min-width: 120px;
    font-weight: 500;

    i {
      font-size: 0.875rem;
    }

    &.btn-primary {
      background-color: #007bff;
      border-color: #007bff;

      &:hover:not(:disabled) {
        background-color: #0056b3;
        border-color: #0056b3;
      }

      &:disabled {
        background-color: #6c757d;
        border-color: #6c757d;
        opacity: 0.65;
        cursor: not-allowed;
      }
    }

    &.btn-outline-secondary {
      color: #6c757d;
      border-color: #6c757d;

      &:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: #fff;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .filter-actions {
    .d-flex {
      .btn {
        width: 100%;
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .field-filters-container {
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 0.5rem;

      .btn {
        align-self: flex-end;
      }
    }
  }

  .filters-list {
    max-height: 300px;
  }

  .filter-actions {
    .btn {
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
    }
  }
}


