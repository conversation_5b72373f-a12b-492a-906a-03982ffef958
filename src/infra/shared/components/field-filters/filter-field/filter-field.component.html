<!-- Filter Field Component -->
<div class="filter-field-container">
  <!-- Checkbox và Label -->
  <div class="field-header d-flex align-items-center">
    <mat-checkbox
      [id]="'filter-' + filter.field._id"
      [checked]="isActive()"
      (change)="handleMatCheckboxChange($event)"
      class="fw-medium">
      {{ filter.field.label }}
    </mat-checkbox>
  </div>

  <!-- Filter Options Panel (Collapse) -->
  <div
    class="filter-options-panel mt-2"
    *ngIf="isActive()">

    <!-- Operator Selection -->
    <div class="d-flex align-items-center w-100 mb-2">
      <mat-form-field appearance="outline" class="w-100">
        <mat-select
          [value]="selectedOperator()"
          (selectionChange)="handleMatOperatorChange($event)"
          [placeholder]="getOperatorPlaceholder() | translate">
          <mat-option
            *ngFor="let operator of availableOperators()"
            [value]="operator.value">
            {{ operator.labelKey | translate }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="d-flex align-items-center w-100" *ngIf="shouldShowInput()">
      <div
        appDynamicFilterInput
        #dynamicInput
        [field]="filter.field"
        [operator]="selectedOperator()"
        [value]="getCurrentFilterValue()"
        [disabled]="false"
        (filterChange)="onDynamicFilterChange($event)">
      </div>
    </div>

  </div>
</div>


