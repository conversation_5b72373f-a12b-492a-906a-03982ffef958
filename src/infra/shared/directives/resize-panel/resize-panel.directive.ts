import { Directive, ElementRef, Input, HostListener, OnInit, OnD<PERSON>roy, inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

// Dịch vụ lưu trữ localStorage
@Injectable({ providedIn: 'root' })
export class StorageService {
  setItem(key: string, value: string): void {
    localStorage.setItem(key, value);
  }

  getItem(key: string): string | null {
    return localStorage.getItem(key);
  }
}

@Directive({
  selector: '[appResizePanel]',
  standalone: true
})
export class ResizePanelDirective implements OnInit, OnDestroy {
  private el = inject(ElementRef);
  private translateService = inject(TranslateService);
  private storageService = inject(StorageService);

  // Inputs
  @Input() leftPanel!: ElementRef<HTMLElement> | HTMLElement;
  @Input() rightPanel!: ElementRef<HTMLElement> | HTMLElement;
  @Input() showLeftPanel: boolean = true;
  @Input() minWidth: number = 200;
  @Input() maxWidth: number = 600;
  @Input() isAbsolute: boolean = false;
  @Input() panelName: string = ''; // Thêm input panelName để định danh panel

  private panelWidthSubject = new BehaviorSubject<number>(this.minWidth);
  panelWidth$: Observable<number> = this.panelWidthSubject.asObservable();

  private get storageKey(): string {
    return this.panelName ? `panel_width_${this.panelName}` : 'panel_width'; // Tạo key duy nhất dựa trên panelName
  }

  private isDragging = false;
  private isHidden = false; // Track panel visibility state
  private savedStyles: { marginLeft: string; width: string } | null = null; // Store original styles
  private isAnimating = false; // Track animation state
  private animationDuration = 200; // Animation duration in ms

  ngOnInit(): void {
    // Khởi tạo chiều rộng từ localStorage hoặc minWidth
    const savedWidth = this.storageService.getItem(this.storageKey);
    const initialWidth = savedWidth ? parseInt(savedWidth, 10) : this.minWidth;
    this.setPanelWidth(initialWidth);

    // Thêm class CSS cho resize handle
    this.el.nativeElement.classList.add('resize-handle');

    // Setup CSS transitions cho animation
    this.setupAnimationStyles();

    // Thêm tooltip dịch từ i18n
    this.translateService.get('RESIZE_PANEL.TOOLTIP').subscribe(tooltip => {
      this.el.nativeElement.setAttribute('title', tooltip);
    });
  }

  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent): void {
    this.isDragging = true;
    // Disable transitions during drag for smooth performance
    this.disableTransitions();
    event.preventDefault();
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (!this.isDragging) return;

    const newWidth = event.clientX;
    if (newWidth >= this.minWidth && newWidth <= this.maxWidth) {
      this.setPanelWidth(newWidth);
    }
  }

  @HostListener('document:mouseup')
  onMouseUp(): void {
    if (this.isDragging) {
      this.isDragging = false;
      // Re-enable transitions after drag
      this.enableTransitions();
    }
  }

  /**
   * Setup CSS transitions cho smooth animation
   */
  private setupAnimationStyles(): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    // Apply CSS transitions cho smooth animation
    const transitionStyle = `width ${this.animationDuration}ms ease-out, margin-left ${this.animationDuration}ms ease-out, opacity ${this.animationDuration}ms ease-out`;

    leftPanelEl.style.transition = transitionStyle;
    rightPanelEl.style.transition = transitionStyle;
  }

  private setPanelWidth(width: number): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    // Cập nhật style dựa trên isAbsolute
    if (this.isAbsolute) {
      // Bố cục absolute
      leftPanelEl.style.width = `${width}px`;
      leftPanelEl.style.position = 'absolute';
      leftPanelEl.style.top = '0';
      leftPanelEl.style.left = '0';
      rightPanelEl.style.marginLeft = `${width + 10}px`;
      rightPanelEl.style.width = `calc(100% - ${width + 10}px)`;
    } else {
      // Bố cục flex
      leftPanelEl.style.width = `${width}px`;
      leftPanelEl.style.position = ''; // Xóa position nếu có
      rightPanelEl.style.marginLeft = ''; // Xóa margin-left
      rightPanelEl.style.width = ''; // Xóa width, để flex-grow xử lý
    }

    // Lưu chiều rộng vào localStorage và cập nhật BehaviorSubject
    this.panelWidthSubject.next(width);
    this.storageService.setItem(this.storageKey, width.toString());
  }

  /**
   * Hide panel với CSS animation
   * Method này được gọi từ component khi cần ẩn panel
   */
  hide(): Promise<void> {
    if (this.isHidden || this.isAnimating) return Promise.resolve(); // Đã ẩn rồi hoặc đang animate

    return new Promise((resolve) => {
      this.isAnimating = true;

      const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
      const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

      // Ensure transitions are enabled for synchronized animation
      this.enableTransitions();

      // Lưu styles hiện tại trước khi reset
      this.savedStyles = {
        marginLeft: rightPanelEl.style.marginLeft || '',
        width: rightPanelEl.style.width || ''
      };

      // Start synchronized animation for both panels using requestAnimationFrame
      requestAnimationFrame(() => {
        // Animate panel collapse and adjacent panel expansion simultaneously
        leftPanelEl.style.width = '0px';
        leftPanelEl.style.opacity = '0';
        rightPanelEl.style.marginLeft = '0px';
        rightPanelEl.style.width = '100%';

        // Update internal state
        this.panelWidthSubject.next(0);
      });

      // Wait for animation to complete
      setTimeout(() => {
        this.isHidden = true;
        this.isAnimating = false;
        resolve();
      }, this.animationDuration);
    });
  }

  /**
   * Show panel với CSS animation
   * Method này được gọi từ component khi cần hiện panel
   */
  show(): Promise<void> {
    if (this.isAnimating) return Promise.resolve(); // Đang animate

    return new Promise((resolve) => {
      this.isAnimating = true;

      const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
      const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

      // Restore lại panel width từ localStorage hoặc minWidth
      const savedWidth = this.storageService.getItem(this.storageKey);
      const currentWidth = savedWidth ? parseInt(savedWidth, 10) : this.minWidth;

      // Ensure transitions are enabled for synchronized animation
      this.enableTransitions();

      // Set initial collapsed state for both panels simultaneously
      leftPanelEl.style.width = '0px';
      leftPanelEl.style.opacity = '0';
      rightPanelEl.style.marginLeft = '0px';
      rightPanelEl.style.width = '100%';

      // Force reflow để đảm bảo initial state được apply
      leftPanelEl.offsetHeight;

      // Start synchronized animation for both panels
      requestAnimationFrame(() => {
        leftPanelEl.style.opacity = '1';
        // leftPanelEl.style.zIndex = '10';
        leftPanelEl.style.width = `${currentWidth}px`;
        rightPanelEl.style.marginLeft = `${currentWidth}px`;
        rightPanelEl.style.width = `calc(100% - ${currentWidth}px)`;

        // Update internal state
        this.panelWidthSubject.next(currentWidth);
      });

      // Wait for animation to complete
      setTimeout(() => {
        this.isHidden = false;
        this.isAnimating = false;
        this.savedStyles = null; // Clear saved styles
        resolve();
      }, this.animationDuration);
    });
  }

  /**
   * Disable transitions temporarily (useful for instant updates during resize)
   */
  private disableTransitions(): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    leftPanelEl.style.transition = 'none';
    rightPanelEl.style.transition = 'none';
  }

  /**
   * Re-enable transitions
   */
  private enableTransitions(): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    const transitionStyle = `width ${this.animationDuration}ms ease-out, margin-left ${this.animationDuration}ms ease-out, opacity ${this.animationDuration}ms ease-out`;
    leftPanelEl.style.transition = transitionStyle;
    rightPanelEl.style.transition = transitionStyle;
  }

  /**
   * Check if panel is currently hidden
   */
  isCurrentlyHidden(): boolean {
    return this.isHidden;
  }

  /**
   * Check if panel is currently animating
   */
  isCurrentlyAnimating(): boolean {
    return this.isAnimating;
  }

  /**
   * Set initial state without animation (for component initialization)
   * @param visible - Whether panel should be visible initially
   */
  setInitialState(visible: boolean): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    // Disable transitions for instant setup
    this.disableTransitions();

    if (visible) {
      // Setup visible state
      const savedWidth = this.storageService.getItem(this.storageKey);
      const currentWidth = savedWidth ? parseInt(savedWidth, 10) : this.minWidth;

      leftPanelEl.style.width = `${currentWidth}px`;
      leftPanelEl.style.opacity = '1';
      rightPanelEl.style.marginLeft = `${currentWidth}px`;
      rightPanelEl.style.width = `calc(100% - ${currentWidth}px)`;

      this.isHidden = false;
      this.panelWidthSubject.next(currentWidth);
    } else {
      // Setup hidden state
      leftPanelEl.style.width = '0px';
      leftPanelEl.style.opacity = '0';
      rightPanelEl.style.marginLeft = '0px';
      rightPanelEl.style.width = '100%';

      this.isHidden = true;
      this.panelWidthSubject.next(0);
    }

    // Re-enable transitions after a small delay
    setTimeout(() => {
      this.enableTransitions();
    }, 50);
  }

  ngOnDestroy(): void {
    this.panelWidthSubject.complete();
  }
}
