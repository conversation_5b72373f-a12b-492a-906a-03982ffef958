<!-- <app-page-context-bar></app-page-context-bar> -->

<app-list-layout
  [config]="config()"
  (filterChange)="onFilterChange($event)"
  (sortChange)="onSortChange($event)"
  (columnsChange)="onColumnsChange($event)"
  [actionsTemplate]="actionsTemplate"
  (onActionClick)="handleActionClick($event)"
  (pageChange)="onPageChange($event)"
  >
  <!-- Template cho cột cuộn ngang -->
  <!-- <ng-template #scrollableColumnsTemplate let-item let-visibleColumns="visibleColumns">
    <div class="scrollable-columns">
      <div class="column-data" *ngIf="visibleColumns.includes('name')">{{ item.name }}</div>
      <div class="column-data" *ngIf="visibleColumns.includes('price')">{{ item.price | currency }}</div>
      <div class="column-data" *ngIf="visibleColumns.includes('stock')">{{ item.stock }}</div>
      <div class="column-data" *ngIf="visibleColumns.includes('category')">{{ item.category }}</div>
    </div>
  </ng-template> -->

  <!-- Template cho hành động -->
  <ng-template #actionsTemplate let-item let-onActionClick="onActionClick">
    <!-- <div class="actions-column">
      <button (click)="onActionClick(item, 'view')">Xem chi tiết</button>
      <button (click)="onActionClick(item, 'edit', { mode: 'quick' })">Sửa nhanh</button>
      <button (click)="hehe()">Xóa</button>
      <div class="actions-dropdown">
        <button (click)="onActionClick(item, 'view')">Xem chi tiết</button>
        <button (click)="onActionClick(item, 'edit', { mode: 'quick' })">Sửa nhanh</button>
        <button (click)="hehe()">Xóa</button>
      </div>
    </div> -->
  </ng-template>

  <!-- Template cho custom filter (khoảng giá) -->
  <!-- <ng-template #priceRangeTemplate let-filterValues let-onChange="onChange">
    <div class="price-range-filter">
      <label>Khoảng giá:</label>
      <input
        type="range"
        min="0"
        max="1000"
        [(ngModel)]="filterValues['priceRange']"
        (ngModelChange)="onChange('priceRange', $event)">
      <span>{{ filterValues['priceRange'] || 0 }} $</span>
    </div>
  </ng-template> -->
</app-list-layout>
