export interface InteractionHistory {
    _id?: string;
    entityId: string;
    entityType: 'contact' | 'lead' | 'customer';
    type: 'call' | 'email' | 'meeting' | 'note' | 'discussion' | 'systemEvent';
    isSystemEvent: boolean;
    eventCode?: string;
    subject: string;
    description: string;
    taggedUsers: string[];
    hashtags: string[];
    taggedEntities?: Array<{
        entityId: string;
        entityType: 'contact' | 'lead' | 'customer';
    }>;
    language?: string;
    contributors: string[];
    createdBy: string;
    createdAt: Date;
    scheduledAt?: Date;
    status: 'completed' | 'pending' | 'cancelled';
    opportunityId?: string;
    attachments?: string[];
    tags?: string[];
}
