import { Place } from '@type/utils/address';
import { Image, Modify } from '@type/utils/common';
import { BusinessDetails } from '@type/shared/business_details';
import { CustomerPhone } from './customer_phone';
import { CustomerFacebook } from './customer_facebook';
import { CustomerZalo } from './customer_zalo';
import { ContactCore } from '../contact';
export type CustomerCoreDetails = {
    title?: string;
    firstName: string;
    lastName?: string;
    fullName?: string;
    mobilePhones: Array<CustomerPhone>;
    testPhone?: CustomerPhone;
    email?: string[];
    address?: Array<Place>;
    sources: string[];
    note?: string;
    avatars?: Image[];
    covers?: Image[];
    customerSegment?: string;
    facebook?: Array<CustomerFacebook>;
    zalo: Array<CustomerZalo>;
    latestUsed: {
        phoneNumber: string;
        address: Place;
    };
    /**
     * để search tiếng việt
     */
    search?: string[];
};
export type CustomerIndividualDetails = CustomerCoreDetails & {
    gender?: 'male' | 'female' | 'other';
    dob?: Date;
    preferences?: {
        [key: string]: string | number | boolean;
    };
};
export type CustomerBusinessDetails = Modify<BusinessDetails, {
    contactPersons?: Array<ContactCore>;
    annualRevenue?: string;
    contracts?: Array<{
        contractId: string;
        signedAt: Date;
        amount: number;
        status: 'active' | 'expired' | 'terminated';
    }>;
    bankAccount?: Array<{
        accountNumber: string;
        bankName: string;
        accountHolder: string;
        accountType?: 'individual' | 'business';
    }>;
    workPhones: Array<CustomerPhone>;
}>;
