import { ZaloSimplifyUserAccount, ZaloUserAccountUid } from '../../ems/store/store_account_zalo';
export type CustomerPhone = {
    phoneNumber: string;
    source: string;
    zalo: {
        zaloUids?: Array<ZaloUserAccountUid>;
        zaloGlobalId?: string;
        hasZalo?: boolean;
        hasZaloFriend?: boolean;
        zaloName?: string;
        zaloAvatar?: string;
        zaloCover?: string;
        zaloBlockMsgFromStranger?: boolean;
        zaloBlockFriendRequest?: boolean;
        lastCheckedZaloDataAt?: Date;
        lastSentZaloFriendRequestAt?: Date;
        acceptedZaloFriendAt?: Date;
        nextCheckZaloDataAt?: Date;
        nextAutoSentZaloFriendRequestAt?: Date;
        /**
         * hỗ trợ nhiều zalo của chủ shop cùng chăm sóc 1 khách hàng
         * vì zalo ra zalo userId cho mỗi một account
         */
        zaloStoreConnections?: Array<ZaloSimplifyUserAccount & {
            userId: string;
            zaloGlobalId: string;
            countSentFriendRequest: number;
            hasFriend: boolean;
        }>;
    };
};
