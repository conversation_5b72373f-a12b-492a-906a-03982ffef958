import { ZaloSimplifyUserAccount, ZaloUserAccountInfo } from '../../ems/store/store_account_zalo';
import { CustomerZaloFriend } from './customer_zalo_friend';
export type CustomerZalo = {
    zaloUserData?: Array<ZaloUserAccountInfo>;
    zaloSentFriendRequests?: Array<{
        sentAt: Date;
        from: ZaloSimplifyUserAccount & {
            userId: string;
        };
        to: ZaloSimplifyUserAccount & {
            userId: string;
        };
        success: boolean;
    }>;
    zaloFriends?: Array<CustomerZaloFriend>;
    lastSentZaloFriendRequestAt?: Date;
    lastSentZaloFriendRequestDate?: string;
    lastUnfriendZaloAt?: Date;
    lastUnfriendZaloDate?: string;
    acceptedZaloFriendAt?: Date;
    hasZalo?: boolean;
    lastCheckedZaloDataAt?: Date;
};
