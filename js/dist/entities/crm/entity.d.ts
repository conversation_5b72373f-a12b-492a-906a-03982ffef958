export interface CRMEntity {
    _id?: string;
    companyId: string;
    storeId: string;
    branchId: string;
    createdBy: {
        _id: string;
        name: string;
    };
    ownerId: {
        _id: string;
        name: string;
    };
    visibility: 'private' | 'company';
    sharedWith?: Array<{
        userId?: string;
        departmentId?: string;
        role?: 'viewer' | 'editor';
    }>;
    shareHistory?: Array<{
        sharedBy: string;
        sharedWith: {
            userId?: string;
            departmentId?: string;
        };
        role: 'viewer' | 'editor';
        sharedAt: Date;
    }>;
    referredBy?: {
        _id: string;
        name: string;
    };
    metadata?: {
        [key: string]: string | number | Date | boolean;
    };
}
