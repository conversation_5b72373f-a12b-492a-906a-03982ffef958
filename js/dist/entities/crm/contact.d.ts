import { Image } from "@type/utils/common";
import { CustomerPhone } from "./customer/customer_phone";
import { CRMEntity } from "./entity";
/**
 * S<PERSON> khác biệt giữa Tiềm năng, <PERSON><PERSON><PERSON> h<PERSON>, và Khách hàng
 * <PERSON><PERSON><PERSON><PERSON> năng (Lead): <PERSON><PERSON> thông tin chi tiết về cá nhân hoặc đại diện tổ chức thu thập từ nhiều
 * nguồn như quảng cáo,chiến dịch tiếp thị, hoặc nhân viên kinh doanh tự tìm kiếm.
 * Đ<PERSON><PERSON> là giai đoạn đầu, chưa phải khách hàng chính thức, cần chăm sóc để chuyển đổi.
 * Tiềm năng có thể được chấm điểm để đánh giá mức độ quan tâm, với các hoạt động như cuộc gọi, email để thúc đ<PERSON>.
 *
 * <PERSON><PERSON><PERSON> (Contact): Đ<PERSON><PERSON><PERSON> định nghĩa là khách hàng cá nhân hoặc người liên hệ của khách hàng tổ chức.
 * Ví dụ, đối với tổ chức, Liên hệ là giám đốc, nhân viên mua hàng; đối với cá nhân,
 * chính là khách hàng đó. Điều này giúp quản lý các điểm tiếp xúc (touchpoints) với tổ chức,
 * với các trường như tên, số điện thoại, email, và chức danh.
 * Liên hệ thường có thông tin liên lạc (số điện thoại, email, chức danh)
 * và vai trò trong tổ chức, giúp quản lý giao tiếp hiệu quả.
 *
 * Khách hàng (Customer): Là khách hàng đã xác nhận, bao gồm cả tổ chức và cá nhân,
 * được quản lý trong phân hệ Khách hàng. Đây là giai đoạn đã có giao dịch,
 * với lịch sử mua hàng, hợp đồng, và các hoạt động chăm sóc lâu dài,
 * với các trường như lịch sử giao dịch, xếp hạng, và thông tin liên lạc chi tiết.
 */
export interface ContactCore {
    title?: string;
    gender?: 'male' | 'female' | 'other';
    dob?: Date;
    note?: string;
    avatars?: Image[];
    covers?: Image[];
    name: string;
    phone: CustomerPhone;
    email: string;
    personalEmail?: string;
    department?: string;
    position?: string;
    isPrimaryContact?: boolean;
    customerId?: string;
}
export type Contact = CRMEntity & ContactCore;
