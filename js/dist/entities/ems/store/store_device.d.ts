export type StoreDevice = {
    _id: string;
    deviceId: string;
    name: string;
    type: string;
    status: 'active' | 'inactive';
    branchId: string;
    config: {
        printer: {
            'name': string;
            'type': 'receipt' | 'label' | 'hybrid';
            'connection': NetworkPrinterConnection | BluetoothPrinterConnection | UsbPrinterConnection | SerialPrinterConnection | CloudPrinterConnection | WifiDirectPrinterConnection | NFCPrinterConnection;
            'status': 'active' | 'inactive';
            'paperSize': '80mm' | '58mm' | '76mm' | 'A4' | 'A5';
            /**
             * máy in nhiệt
             */
            labelPaperSize: LabelPaperSize;
            labelPrinterSettings: {
                'dpi': number;
                'barcodeEnabled': boolean;
                'qrEnabled': boolean;
            };
            'labelCapable': boolean;
            'area': 'cashier' | 'bar' | 'kitchen_hot' | 'kitchen_cold' | 'all';
        };
    };
    settings: {
        phone: {
            autoSyncCallLogs: boolean;
            autoSyncMessages: boolean;
            autoSyncContacts: boolean;
            /**
             * tự động tạo customer mới từ contact
             */
            autoCreateCustomerFromContact: boolean;
            /**
             * tự động tạo customer mới từ call logs
             */
            autoCreateCustomerFromCallLogs: boolean;
            /**
             * tự động tạo customer mới từ messages
             */
            autoCreateCustomerFromMessages: boolean;
        };
    };
    createdAt: Date;
    updatedAt: Date;
};
type NetworkPrinterConnection = {
    'type': 'network';
    'ip': string;
    'port': number;
};
type BluetoothPrinterConnection = {
    'type': 'bluetooth';
    'macAddress': string;
    'deviceName': string;
    'bluetoothVersion': string;
    'pin': string;
    'pairedStatus': boolean;
};
type UsbPrinterConnection = {
    'type': 'usb';
    deviceId: string;
    port: string;
};
type SerialPrinterConnection = {
    'type': 'serial';
    baudRate: number;
    port: string;
};
type CloudPrinterConnection = {
    'type': 'cloud';
    cloudService: string;
    'printerToken': string;
    'endpoint': string;
};
type WifiDirectPrinterConnection = {
    'type': 'wifi_direct';
    'ssid': string;
    'password': string;
};
type NFCPrinterConnection = {
    'type': 'nfc';
    'deviceId': string;
};
type LabelPaperSize = {
    'width': '25mm';
    'height': '15mm';
} | {
    'width': '40mm';
    'height': '30mm';
} | {
    'width': '50mm';
    'height': '25mm';
} | {
    'width': '100mm';
    'height': '50mm';
} | 'custom';
export {};
