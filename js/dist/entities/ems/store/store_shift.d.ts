/**
 * L<PERSON><PERSON> thông tin về các ca làm việc do quản lý thiết lập.
 * Dùng để quản lý các ca làm việc chung (VD: <PERSON><PERSON> sáng, chiều, tối).
 * Không thay đổi theo từng nhân viên, chỉ dùng để reference.
 *
 * Collection này lưu trữ thông tin về các ca làm việc do quản lý thiết lập. Nó giúp:
 * Định nghĩa giờ làm việc cho từng ca (sáng, chiều, tối, ca linh hoạt, ca đặc biệt, ca gấp).
 * Giúp nhân viên biết họ có thể đăng ký làm việc vào ca nào.
 * Hỗ trợ giới hạn số lượng nhân viên tối thiểu/tối đa cho từng ca.
 * Giú<PERSON> quản lý theo dõi nhân viên có đang làm đúng ca không.
 * Hỗ trợ báo cáo năng suất theo từng ca làm.
 *
 *
 * Tình huống: Quản lý một quán cà phê có ba ca làm việc cố định.
 *  Ca sáng của quán từ 06:00 - 12:00.
 *  Cần ít nhất 3 nhân viên mới mở ca, tối đa 6 người.
 *  Đây là ca cố định, không thay đổi theo mùa vụ.
 *  Nhân viên có thể đăng ký hoặc được xếp lịch theo ca này.
 *
 *
 * Tình huống: Một siêu thị mở cửa 24/7, có ca đêm, nhưng số nhân viên cần thiết thay đổi theo ngày trong tuần.
 *  Ca đêm từ 22:00 - 06:00.
 *  Đây là ca linh hoạt, số nhân viên cần có thể thay đổi.
 *  Mùa cao điểm (Lễ Tết, Black Friday) có thể tăng thêm người.
 *
 * Tình huống: Nhà hàng chuyên tổ chức tiệc cưới vào cuối tuần, cần thêm nhân viên part-time vào những ngày đó.
 *  Ca này chỉ có vào thứ Bảy & Chủ Nhật, khi có đặt tiệc.
 *  Cần 10 - 20 nhân viên để phục vụ khách.
 *  Đây là ca theo sự kiện, không diễn ra hàng ngày.
 *
 * Tình huống: Một công ty giao hàng có ca tăng cường vào ngày Flash Sale Shopee/Lazada.
 *  Ca đặc biệt cho ngày giảm giá lớn (Flash Sale).
 *  Cần thêm nhân viên giao hàng để đáp ứng nhu cầu tăng cao.
 *  Số lượng nhân viên có thể lên đến 30 người.
 */
export interface StoreShift {
    '_id': string;
    companyId: string;
    'storeId': string;
    'branchId': string;
    'name': string;
    'startTime': {
        hours: number;
        minutes: number;
    };
    'endTime': {
        hours: number;
        minutes: number;
    };
    'type': 'fixed' | 'flexible' | 'night' | 'temporary' | 'special' | 'custom';
    'seasonal': boolean;
    'minEmployees': number;
    'maxEmployees': number;
    createdBy: {
        _id: string;
        name: string;
    };
    'createdAt': Date;
}
