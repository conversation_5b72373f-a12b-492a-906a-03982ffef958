import { RequestGlobalOptions } from 'ndx_request';
import { Modify } from '@type/utils/common';
import { StoreAccount } from './store_account';
export type StoreZaloAccount = Modify<StoreAccount, {
    username?: string;
    token?: string;
    accountConfigs?: {
        proxy?: RequestGlobalOptions['proxy'];
    };
    configs?: {};
}>;
export type ZaloSimplifyUserAccount = {
    zaloGlobalId: string;
    zaloName: string;
    username: string;
    phoneNumber: string;
};
/**
 * Zalo user uid mỗi một tài khoản get lại ra 1 uid
 * ví dụ cùng một số đt xxx
 * nhưng lấy info từ số yyy ra 1 uid
 * từ zzz ra 1 uid
 */
export type ZaloUserAccountUid = {
    uid: string;
    from: {
        uid: string;
        phone: string;
        name: string;
    };
};
export type ZaloUserAccountInfo = ZaloSimplifyUserAccount & {
    zaloUids?: Array<ZaloUserAccountUid>;
    unchanged_profiles?: any;
    phonebook_version?: number;
    displayName: string;
    avatar: string;
    bgavatar: string;
    cover: string;
    gender: number;
    dob: number;
    sdob: string;
    status: string;
    isFr: number;
    isBlocked: number;
    lastActionTime: number;
    lastUpdateTime: number;
    isActive: number;
    key: number;
    type: number;
    isActivePC: number;
    isActiveWeb: number;
    isValid: number;
    userKey: string;
    accountStatus: number;
    oaInfo: any;
    user_mode: number;
    globalId: string;
    bizPkg: {
        label: any;
        pkgId: number;
    };
    createdTs: number;
    oa_status: any;
    /**
     * rebuild
     */
    isBusiness?: boolean;
};
