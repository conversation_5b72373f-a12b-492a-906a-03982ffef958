import { Place } from '@type/utils/address';
import { FEATURES } from '@constants/features.const';
import { PACKAGES } from '@constants/packages.const';
export type Store = {
    companyId: string;
    companyName: string;
    storeName: string;
    storeId: string;
    branchName: string;
    'packages': Array<((typeof PACKAGES)[number])>;
    /**
     * enable các tính năng trong gói
     * để trong service kiểm tra
     */
    'enabledFeatures': {
        [k in ((typeof FEATURES)[number])]: boolean;
    };
    owners: Array<{
        _id: string;
        name: string;
    }>;
    /**
     * tất cả query theo branchId này
     * mongooseId
     */
    branchId: string;
    shareDataWithBranchIds?: string[];
    warehouseIds: string[];
    address: string;
    addressInfo: Place & {
        region: string;
        regionName: string;
        /**
         * match voi SearchPlaceParams trong ndx_maps searchPlaceByAddress
         */
        regionSearch: string;
    };
    openingDate: Date;
    phone: string;
    website?: string;
    settings: {
        'general': {
            'invoice': {
                'autoPrintInvoice': boolean;
                'allowTemporaryInvoice': boolean;
            };
        };
        sales: {
            'inventory': {
                'hideOutOfStockProducts': boolean;
                'allowSalesWhenOutOfStock': boolean;
            };
            'returns': {
                'crossBranchReturns': boolean;
                'exchangeOnly': boolean;
                'noRefundForLowerValueReturns': boolean;
                'singleReturnPolicy': boolean;
            };
            'orders': {
                'requireCustomerInfo': boolean;
                'disallowDebtOrders': boolean;
                'allowOrderCancellationAtPos': boolean;
                'disableDeliveryOption': boolean;
                'bulkProductImportToCart': boolean;
                'sendOrderConfirmationEmail': boolean;
                'autoSendNewOrderInvoice': boolean;
                'defaultSendOrderInTransitStatus': boolean;
                pos: {
                    'autoVerifyPosOrders': boolean;
                    'hideOnlineOrdersAtPos': boolean;
                };
                'promotions': {
                    'enableProductBasedPromotions': boolean;
                    'enableOrderDiscount': {
                        'cash': boolean;
                        'percentage': boolean;
                    };
                };
            };
            /**
             * cho phép bán số lượng số thập phân
             * ví dụ amount 0.5
             */
            allowFractionalQuantity: boolean;
            timezone: string;
            currency: string;
        };
        shift: {
            /**
             * tự động duyệt duyệt hoặc từ chối yêu cầu đổi ca dựa trên nhu cầu thực tế.
             */
            'shiftChangeApprovalRequired': boolean;
        };
        warehouse: {
            /**
             * Nếu restrictWarehouseAccess = true, nhân viên chỉ được chọn kho được phân công.
             */
            restrictWarehouseAccess: boolean;
        };
        bill?: {
            logo?: string;
            phone: string;
            website?: string;
        };
    };
    templates: {
        /**
         * hóa đơn nhiệt
         * Nếu hóa đơn in từ máy in nhiệt là biên lai khách nhận sau khi thanh toán:
         * Gọi là "receipt". Đây là cách dùng phổ biến nhất trong hệ thống POS và cửa hàng ăn uống.
         *
         * Nếu hóa đơn in ra để khách kiểm tra trước khi thanh toán (hóa đơn tạm tính): Gọi là "bill".
         * Nếu là hóa đơn chính thức, có giá trị pháp lý: Gọi là "invoice",
         * nhưng hiếm khi dùng máy in nhiệt cho mục đích này.
         */
        bill: {
            type: 'default' | 'custom' | 'html';
            html: string;
        };
        /**
         * hóa đơn gửi qua facebook/zalo
         */
        onlineBill: {
            type: 'default' | 'custom' | 'html';
            message: string;
        };
        delivery: {
            inTransitMessage: string;
        };
    };
    paymentMethods: Array<BankPaymentMethod>;
};
export type BankPaymentMethod = {
    'type': 'bank';
    'provider': string;
    'accountName': string;
    'accountNumber': string;
    'branch': string;
    baseQrCode: string;
};
