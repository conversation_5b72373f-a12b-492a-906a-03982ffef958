import { Time } from '@type/utils/common';
/**
 * Collection holiday_schedules dùng để quản lý ngày nghỉ lễ,
 * gi<PERSON><PERSON> hệ thống tính công, ca làm việc, và lương thưởng chính xác.
 *
 * Tất cả nhân viên đều không có userShift vào ngày này.
 *
 * 1. Dùng để tự động xác định ngày lễ khi tính công
 * Nếu nhân viên làm việc trong ngày lễ → Tăng lương theo hệ số (VD: X2 lương).
 * Nếu là ngày nghỉ chính thức → Không lên lịch làm việc tự động.
 * 🔹 Ví dụ: Lưu ngày nghỉ Tết Nguyên Đán
 *
 * 2. Ngày lễ riêng của công ty/cửa hàng (<PERSON><PERSON><PERSON> kỷ niệm, sự kiện nội bộ...)
 * 📌 Dùng để thiết lập lịch nghỉ riêng cho từng công ty/cửa hàng
 *
 * Ví dụ: Một cửa hàng có thể nghỉ vào ngày khai trương hằng năm.
 * 🔹 Ví dụ: Lưu ngày nghỉ khai trương của cửa hàng
 *
 * 3. Ngày nghỉ đặc biệt của từng nhân viên (nghỉ phép, nghỉ cưới, nghỉ tang...)
 * 📌 Dùng để lưu các ngày nghỉ cá nhân, có thể duyệt hoặc không
 * Khi nhân viên nghỉ phép, hệ thống cập nhật vào đây để tránh tự động tạo ca làm.
 * 🔹 Ví dụ: Nhân viên xin nghỉ cưới
 *
 * Nếu đã có leave_requests, tại sao holiday_schedules cần thêm loại "personal"
 * (ví dụ: nghỉ phép, nghỉ cưới, nghỉ tang)?

* 🔥 Khi nào cần holiday_schedules type = "personal"
* 1️⃣ 📌 Lưu lại các ngày nghỉ cá nhân quan trọng (mang tính hệ thống)

* Ví dụ: Công ty có chính sách nghỉ cưới 3 ngày, nghỉ tang 5 ngày, không cần nhân viên tự gửi leave_requests.
* Hệ thống sẽ tự động tạo ngày nghỉ này cho nhân viên.
* 2️⃣ 📌 Hỗ trợ kiểm tra lịch nghỉ nhanh chóng

* Khi nhân viên check lịch làm việc, hệ thống có thể dễ dàng hiển thị lịch nghỉ cá nhân mà không cần duyệt qua leave_requests.
* Ví dụ: Khi quản lý muốn xem nhân viên nào nghỉ trong tháng, họ chỉ cần truy vấn holiday_schedules.
* 3️⃣ 📌 Dùng cho báo cáo tổng hợp & chính sách nhân sự

* Khi HR xuất báo cáo, họ có thể lọc xem nhân viên đã sử dụng bao nhiêu ngày nghỉ phép, nghỉ cưới, nghỉ tang mà không cần truy vấn nhiều bảng.

* Nghỉ lễ chung (Tết, Quốc Khánh, 30/4, 1/5):	Dùng holiday_schedules
* Nghỉ phép năm theo quy định công ty:	Dùng holiday_schedules (tự động sinh record theo chính sách cty)
* Nghỉ cưới, nghỉ tang (chính sách công ty):	Dùng holiday_schedules (tự động sinh record theo chính sách cty)
* Nghỉ ốm, nghỉ không lương, nghỉ phép cá nhân:	Dùng user_leave_requests
* Nhân viên xin nghỉ đột xuất:	Dùng user_leave_requests
*
* Ví dụ:
* Khi nhân viên có đủ thâm niên, hệ thống tự động tạo ngày nghỉ phép cho họ.
* Hệ thống tự động tạo 3 ngày nghỉ khi nhân viên khai báo kết hôn.
* Hệ thống tự động tạo 5 ngày nghỉ nếu nhân viên khai báo có tang.
 */
export interface StoreHolidaySchedule {
    _id: string;
    companyId: string;
    storeId: string;
    branchId: string;
    name: string;
    type: 'public' | 'personal' | 'special' | 'store';
    description: string;
    startDate: Date;
    endDate: Date;
    'duration': number;
    'autoGenerated': boolean;
    time: Time;
    reviewedBy: {
        _id: string;
        name: string;
    };
    'approved': boolean;
}
