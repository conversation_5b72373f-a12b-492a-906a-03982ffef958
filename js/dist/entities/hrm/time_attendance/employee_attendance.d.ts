/**
 * References:
 * - file://./../../../../docs/features/hrm/employee_attendance.md
 */
import { Time } from '../../../types/utils/common';
export interface EmployeeAttendance {
    id: number;
    employee: {
        _id: string;
        userId: string;
        name: string;
    };
    companyId: string;
    'storeId': string;
    branchId: string;
    'clockIn': Date;
    'clockOut': Date;
    'checkInSource': AttendanceCheckSource;
    'checkOutSource': AttendanceCheckSource;
    'checkInDevice': string;
    'checkOutDevice': string;
    'checkInLocation': string;
    'checkOutLocation': string;
    'adjustments': Array<{
        'type': 'CHECKIN' | 'CHECKOUT';
        'oldTime': Date;
        'newTime': Date;
        'requestedBy': {
            _id: string;
            name: string;
        };
        'approvedBy': {
            _id: string;
            name: string;
        };
        'approvedAt': Date;
        'reason': string;
        evidenceImages: Array<{
            url: string;
        }>;
    }>;
    'status': 'pending' | 'approved' | 'rejected' | 'normal';
    date: string;
    /**
     * thời gian để query report theo ngày hoặc theo tháng
     */
    time: Time;
    createdAt: Date;
    updatedAt: Date;
}
type AttendanceCheckSource = 'qr_code' | 'gps' | 'pos' | 'time_attendance_machine' | 'card' | 'fingerprint' | 'face' | 'manual';
export {};
