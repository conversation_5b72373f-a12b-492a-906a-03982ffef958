/**
 * References:
 * - file://./../../../docs/features/hrm/employee_payroll.md
 */
import { Time } from '@type/utils/common';
import { EmployeeBonus, EmployeePenalty } from '@entities/hrm/employee_branch';
/**
 * (<PERSON><PERSON>ơng & thưởng/phạt)
 * Tự động tính lư<PERSON>, thưởng KPI, phạt đi trễ/về sớm.
 * Tích hợp vào bảng báo cáo lương theo từng nhân viên.
 */
export interface EmployeePayroll {
    id: number;
    employee: {
        _id: string;
        userId: string;
        name: string;
    };
    companyId: string;
    storeId: string;
    branchId: string;
    /**
     * L<PERSON>ơng cơ bản
     */
    baseSalary: number;
    workingHours: number;
    overtimeHours: number;
    totalNightShiftHours: number;
    totalBonuses: number;
    totalPenalties: number;
    finalSalary: number;
    dailyRecords: Array<{
        date: string;
        shift: {
            _id: string;
            shiftId: string;
        };
        checkIn: string;
        checkOut: string;
        workedHours: number;
        overtimeHours: number;
        nightShiftHours: number;
        bonus: number;
        penalty: number;
        note: string;
    }>;
    bonuses: Array<{
        date: string;
        type: EmployeeBonus;
        amount: number;
        reason: string;
        /**
         * thưởng khi đạt điều kiện KPI
         */
        relatedKpiRuleId: string;
    }>;
    penalties: Array<{
        date: string;
        type: EmployeePenalty;
        amount: number;
        reason: string;
    }>;
    note: string;
    month: string;
    /**
     * thời gian để query report theo ngày hoặc theo tháng
     */
    time: Time;
    createdAt: Date;
    updatedAt: Date;
}
