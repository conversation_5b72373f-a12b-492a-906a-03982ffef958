/**
 * References:
 * - file://./../../../docs/schema/hrm/employee_branch.md
 */
import { DeviceTokenOnServer } from '@type/utils/device_token';
import { AuthorizationRoleInterface } from '@type/authorization/authorization_role';
/**
 * - Mỗi nhân viên có group, roles, permisssion.
 * 1 user có thể có nhiều roles.
 * Một nhóm có thể có một hoặc nhiều roles.
 * Permission được phân mặc định theo roles, nhưng từng user admin cũng có thể chỉnh
 * sửa permission để có những nhân viên hạn chế 1 số permission nhất định mặc dù nằm cùng 1 group.
 *
 * check xem user có mua gói X, Y, Z hay không mới được access vào tính năng,
 * và xem settings cửa hàng nó enable tính năng đó không.
 *
 * <PERSON>uản lý lương, thưởng
 * Chấm công theo máy chấm công, POS, QR code động, GPS, nhận diện khuôn mặt vân tay, G<PERSON><PERSON><PERSON> hạn 1 thiết bị chấm công
 * Quản lý KPI: Tổng doanh số, Số lượng đơn hàng, Số khách hàng mới, Tỷ lệ chốt đơn, Giá trị đơn trung bình, Thời gian phục vụ, Tỷ lệ hoàn hàng
 * Thưởng theo KPI, tự động tạo khi nhân viên đạt KPI nhưng phải chờ quản lý duyệt hoặc tự động duyệt do setting của cửa hàng
 * Quản lý thêm thưởng, phạt cho từng nhân viên
 * Tự động tính số giờ làm & lương theo dữ liệu chấm công.
 * Xuất báo cáo công & lương theo từng nhân viên.
 * phân ca linh hoạt: Tính lương chính xác theo giờ thực tế hoặc theo ca làm; Hỗ trợ nhân viên làm nhiều ca, không bị cứng nhắc.
 *  giám sát nhân viên thông qua gps để xem lộ trình di chuyển hằng ngày
 *
 *
 * Ngoài lương cơ bản, hệ thống có thể tính thêm:
 *  Thưởng chuyên cần: Đi làm đủ số ca trong tháng.
 *  Thưởng ca đêm: Nếu làm việc sau 22h có thể nhân hệ số x1.5.
 *  Phạt đi trễ/về sớm: Nếu ca làm là 8h-12h mà nhân viên chỉ làm 9h-11h, thì bị trừ tiền theo giờ.
 *
 *  Ví dụ: Nếu đi trễ 1 giờ, hệ số phạt là 1.2, lương 40.000 VNĐ/giờ:
 *  → Tiền bị trừ = 1 × 40.000 × 1.2 = 48.000 VNĐ
 */
export type EmployeeBranch = {
    _id: string;
    employeeId: string;
    positions: string[];
    groups: string[];
    roles: string[];
    'permissions': AuthorizationRoleInterface['permissions'];
    isCompanyOwner: boolean;
    isStoreOwner: boolean;
    isStoreManager: boolean;
    status: 'active' | 'inactive' | 'deleted';
    companyId: string;
    storeId: string;
    branchId: string;
    'salary': {
        'baseSalary': number;
        'baseSalaryPerHour': number;
        'baseSalaryPerShift': number;
        'bonuses': Array<{
            'type': EmployeeBonus;
            'amount': number;
            multiplier?: number;
        }>;
        'penalties': Array<{
            'type': EmployeePenalty;
            'amount': number;
            multiplier?: number;
        }>;
    };
    startWorkingDate: Date;
    stopWorkingDate?: Date;
    /**
     * Với nhân viên làm việc cố định
     * hệ thống sẽ tự sinh UserShift theo schedule này
     *
     * còn nếu không có shiftSchedule:
     * Trường hợp 1: Nhân viên đăng ký ca thủ công
     * Phù hợp cho: Nhân viên part-time, nhân viên làm theo nhu cầu.
     * Luồng xử lý:
     * 1️⃣ Nhân viên tự chọn ca từ danh sách ca có sẵn (shifts).
     * 2️⃣ Hệ thống tạo employee_shifts statú = pending, sau khi quản lý duyệt "status": "scheduled".
     *
     * Trường hợp 2: Quản lý gán ca thủ công
     * Phù hợp cho: Nhân viên thời vụ, nhân viên làm theo yêu cầu quản lý.
     * Luồng xử lý:
     * 1️⃣ Quản lý chọn nhân viên và gán vào ca làm.
     * 2️⃣ Hệ thống tạo employee_shifts ngay lập tức.
     *
     * Trường hợp 3: Nhân viên làm theo yêu cầu (auto-assign)
     * Phù hợp cho: Nhân viên làm việc linh hoạt, làm khi có nhu cầu.
     * Luồng xử lý:
     * 1️⃣ Hệ thống kiểm tra số lượng nhân viên cần cho ca làm.
     * 2️⃣ Nếu thiếu nhân viên, hệ thống tự động gán ca cho nhân viên có ít ca nhất.
     * 3️⃣ Tạo employee_shifts dựa trên nhân viên phù hợp.
     *
     * 🔹 Ví dụ:
     * 🔍 Ca tối ngày 27/02/2025 cần 3 nhân viên, nhưng chỉ có 2 nhân viên đăng ký.
     * 📌 Hệ thống tự động chọn emp_C để đi làm.
     */
    shiftSchedules: Array<{
        shiftId: string;
        day: number[];
        'startDate': Date;
        'endDate': Date;
        'status': 'active' | 'inactive';
    }>;
    /**
     * giới hạn chỉ được checkin trên 1 (hoặc 1 số)
     * thiết vị, phải được accept của owner
     *
     * để tránh user dùng tài khoản đăng nhập trên nhiều thiết bị checkin từ xa
     * hoặc nhờ người khác checkin hộ
     * thiết bị được cho phép checkin phải verìy khuôn mặt, vân tay
     * mỗi lần checkin để xem có đúng chính chủ không
     * tránh trường hợp checkin hộ
     */
    'checkinDevices': string[];
    /**
     * Lưu thông tin nhân viên và danh sách kho họ có quyền truy cập.
     */
    'assignedWarehouses': Array<{
        _id: string;
        name: string;
    }>;
    deviceTokens?: Array<DeviceTokenOnServer>;
    activeDeviceTokens?: Array<DeviceTokenOnServer>;
};
export type EmployeePenalty = 'late' | 'damage' | 'early_leave' | 'bad_review' | 'custom';
export type EmployeeBonus = 'KPI' | 'night_shift' | 'attendance' | 'custom';
