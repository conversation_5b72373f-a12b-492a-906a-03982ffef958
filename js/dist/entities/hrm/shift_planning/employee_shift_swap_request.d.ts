/**
 * References:
 * - file://./../../../docs/features/hrm/employee_shift_swap_request.md
 */
/**
 * <PERSON><PERSON><PERSON> yêu cầu đổi ca của nhân viên.
 */
export interface EmployeeShiftSwapRequest {
    _id: string;
    employee: {
        _id: string;
        userId: string;
        name: string;
    };
    companyId: string;
    storeId: string;
    branchId: string;
    shiftId: string;
    date: string;
    requestedBy: {
        employeeId: string;
        name: string;
    };
    requestedTo: {
        employeeId: string;
        name: string;
    };
    reviewedBy: {
        employeeId: string;
        name: string;
    };
    /**
     *
     * N<PERSON><PERSON> qu<PERSON> l<PERSON>, hệ thống:
     *  Cập nhật userShift.userId = requestedTo._id
     * Ghi log lại giao dịch đổi ca
     */
    status: 'pending' | 'approved' | 'rejected';
    createdAt: string;
    updatedAt: string;
}
