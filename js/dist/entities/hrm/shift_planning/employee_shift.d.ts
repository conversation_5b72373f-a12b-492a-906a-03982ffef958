/**
 * References:
 * - file://./../../../docs/features/hrm/employee_shift.md
 */
import { Time } from '@type/utils/common';
/**
 * EmployeeShift được dùng để lên lịch làm việc (kế ho<PERSON>ch).
 *
 * EmployeeAttendance được tạo sau khi nhân viên chấm công (thực tế).
 * Khi nhân viên check-in/check-out, hệ thống cập nhật dữ liệu chấm công, tính công và lưu vào IEmployeePayroll.
 *
 * EmployeeShift Sinh trước một khoảng thời gian cố định
 * Hệ thống sẽ tạo sẵn lịch employee_shifts cho 1 tuần, 2 tuần hoặc 1 tháng tùy vào chính sách công ty.
 * Luồng xử lý
 *  Hệ thống lấy danh sách employee_shift_schedules có trạng thái "active".
 * <PERSON><PERSON>m tra xem ngày hôm nay có nằm trong danh sách holidays không.
 * <PERSON><PERSON><PERSON> có, tạo một bản ghi mới trong employee_shifts.
 * Nếu nhân viên có yêu cầu nghỉ (leave_requests), bỏ qua không tạo.
 *
 * Nhân viên có thể thấy lịch làm trước.
 * Quản lý có thể chỉnh sửa nếu có thay đổi.
 */
export interface EmployeeShift {
    _id: string;
    /**
     * ban đầu là employee của nhân viên gốc
     * khi nhân viên gốc yêu cầu đổi ca cho nhân viên B và được duyệt
     * thì update id = nhân viên B
     */
    employee: {
        _id: string;
        userId: string;
        name: string;
    };
    companyId: string;
    storeId: string;
    branchId: string;
    /**
     * "approved": Nhân viên có lịch làm, chưa check-in
     * "checked-in": Đã check-in, đang làm việc
     * "completed": Đã check-out, ca làm hoàn thành
     * "absent": Không check-in, vắng mặt không phép
     * "canceled": Nhân viên báo nghỉ trước, ca bị hủy
     */
    'status': 'approved' | 'checked_in' | 'completed' | 'absent' | 'canceled' | 'scheduled';
    'swapRequest': Array<{
        'requestedBy': {
            employeeId: string;
            name: string;
        };
        reviewedBy: {
            employeeId: string;
            name: string;
        };
        'status': 'pending' | 'approved' | 'rejected';
    }>;
    'checkIn': string;
    'checkOut': string;
    'workedHours': number;
    'lateMinutes': number;
    'earlyLeaveMinutes': number;
    'extraHours': number;
    'overtime': boolean;
    'date': string;
    'createdAt': Date;
    updatedAt: Date;
    /**
     * thời gian để query report theo ngày hoặc theo tháng
     */
    time: Time;
}
