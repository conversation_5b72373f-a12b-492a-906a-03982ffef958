/**
 * References:
 * - file://./../../../docs/features/hrm/employee_leave_request.md
 */
import { Time } from '@type/utils/common';
/**
 * L<PERSON>u yêu cầu nghỉ phép của nhân viên.
 * N<PERSON><PERSON> đư<PERSON> duyệt, hệ thống sẽ hủy ca trong employee_shift.
 *
 * 🎯 Triển khai luồng xử lý
* 📌 Nhân viên xin nghỉ
* 1️⃣ Nhân viên gửi yêu cầu vào leave_requests
* 2️⃣ Nếu duyệt → Cập nhật employee_shifts.status = "canceled"
* 3️⃣ Nếu từ chối → Không thay đổi employee_shifts
*
* 📌 Nhân viên đổi ca
* 1️⃣ Nhân viên A gửi yêu cầu vào shift_swap_requests
* 2️⃣ Nếu duyệt → Cập nhật employee_shifts.employeeId = B
* 3️⃣ Nếu từ chối → Không thay đổi gì
*
* 📌 Ng<PERSON><PERSON> lễ chung
* 1️⃣ <PERSON>hi tạo ca làm → Ki<PERSON>m tra holiday_schedules
* 2️⃣ Nếu có ngà<PERSON> lễ → Không tạo employee_shifts
 */
export interface EmployeeLeaveRequest {
    _id: string;
    employee: {
        _id: string;
        userId: string;
        name: string;
    };
    companyId: string;
    storeId: string;
    branchId: string;
    shiftId: string;
    date: string;
    'reason': string;
    requestedBy: {
        _id: string;
        name: string;
    };
    reviewedBy: {
        _id: string;
        name: string;
    };
    /**
     * Nếu được duyệt, hệ thống cập nhật emplyeeShift.status = "canceled".
     * Nếu bị từ chối, nhân viên vẫn phải đi làm.
     */
    status: 'pending' | 'approved' | 'rejected';
    createdAt: Date;
    updatedAt: Date;
    /**
     * thời gian để query report theo ngày hoặc theo tháng
     */
    time: Time;
}
