/**
 * References:
 * - file://./../../../docs/features/hrm/employee_daily_route.md
 */
import { Time } from '@type/utils/common';
/**
 * giám sát lộ trình nhân viên  thông qua gps để xem lộ trình di chuyển hằng ngày,
 * vì nhân viên sale nếu không giám sát chả biết họ có đi làm hay đi uống cafe
 *
 * Thu thập dữ liệu GPS qua mobile app.
 * Vẽ lộ trình trên bản đồ bằng Google Maps / Mapbox.
 * Phát hiện bất thường: <PERSON>h<PERSON><PERSON> di chuyển, ra khỏi khu vực làm việc.
 * Lưu báo cáo giám sát để quản lý theo dõi.
 * Gửi cảnh báo khi nhân viên có dấu hiệu gian lận.
 */
export interface EmployeeDailyRoute {
    _id: string;
    employee: {
        _id: string;
        userId: string;
        name: string;
    };
    companyId: string;
    storeId: string;
    branchId: string;
    /**
     * ngày làm việc
     */
    date: string;
    'totalDistance': number;
    'checkpoints': Array<{
        'time': string;
        'location': {
            'lat': number;
            'lng': number;
        };
    }>;
    'status': 'completed' | 'pending';
    /**
     * thời gian để query report theo ngày hoặc theo tháng
     */
    time: Time;
    createdAt: Date;
    updatedAt: Date;
}
