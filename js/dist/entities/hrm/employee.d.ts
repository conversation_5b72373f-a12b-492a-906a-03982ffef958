/**
 * References:
 * - file://./../../../docs/schema/hrm/employee.md
 */
export type Employee = {
    _id: string;
    /**
     * Nhân viên không cần đăng nhập
    - **Tình huống**: M<PERSON><PERSON> số nhân viên (như lao động thời vụ) không cần tài khoản User, nhưng vẫn được quản lý trong HRM.
    - **Yêu cầu**: Employee tồn tại độc lậ<PERSON>, không bắt buộc gắn với User.
    - **Rủi ro nếu không tách**: <PERSON><PERSON><PERSON><PERSON> tạo User giả cho nhân viên thời vụ, làm hệ thống rối.
     */
    userId?: string;
    /**
     * liên kết với employee_branch
     */
    employeeBranchIds: string[];
    name: string;
    profilePictures?: Array<{
        _id: string;
        url: string;
    }>;
    profileIdPictures?: Array<{
        _id: string;
        url: string;
    }>;
};
