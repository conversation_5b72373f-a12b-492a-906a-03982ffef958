/**
 * References:
 * - file://./../../../docs/features/hrm/employee_activity_log.md
 */
export interface EmployeeActivityLog {
    _id: string;
    userId: string;
    employee: {
        _id: string;
        userId: string;
        name: string;
    };
    role: string;
    'action': 'create' | 'update' | 'delete' | 'show' | 'hide' | 'import' | 'select';
    'entityType': 'product' | 'order' | 'customer';
    'entityId': string;
    'entityName': string;
    'details': {
        'fieldChanged': string;
        'oldValue': string;
        'newValue': string;
    };
    'timestamp': Date;
    'ipAddress': string;
}
