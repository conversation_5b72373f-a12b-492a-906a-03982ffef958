/**
 * References:
 * - file://./../../../docs/schema/hrm/employee_kpi_rule.md
 */
import { EmployeeKpi } from './employee_kpi';
/**
 * tạo KPI mới cho một nhân viên hoặc một nhóm nhân viên
 * Nếu đạt điều kiện, tạo thưởng trong payroll bonus
 */
export type EmployeeKpiRule = {
    _id: string;
    companyId: string;
    'storeId': string;
    'branchId': string;
    name: string;
    description: string;
    'applyTo': 'group' | 'employee';
    'applyToIds': string[];
    'criteria': Array<{
        'metric': keyof EmployeeKpi['metrics'];
        'condition': '>=' | '<=' | '==' | '!=';
        'value': number;
    }>;
    'reward': {
        'amount': number;
        'type': 'cash' | 'voucher' | 'bonus_point';
    };
    'approvalRequired': boolean;
    'active': boolean;
    createdAt: Date;
    updatedAt: Date;
};
