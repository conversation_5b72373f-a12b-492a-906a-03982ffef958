/**
 * References:
 * - file://./../../../docs/schema/hrm/employee_kpi.md
 */
export interface EmployeeKpi {
    _id: string;
    userId: string;
    companyId: string;
    storeId: string;
    branchId: string;
    groups: string[];
    employee: {
        _id: string;
        userId: string;
        name: string;
    };
    'month': string;
    'metrics': {
        'totalRevenue': number;
        'orderCount': number;
        'newCustomers': number;
        'conversionRate': number;
        'avgOrderValue': number;
        'serviceTime': number;
        'returnRate': number;
    };
    createdAt: Date;
    updatedAt: Date;
}
