"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Đ<PERSON><PERSON> nghĩa kiểu cho Lo<PERSON>i vị trí (enum để giới hạn giá trị hợp lệ)
var LocationType;
(function (LocationType) {
    LocationType["Warehouse"] = "Warehouse";
    LocationType["Zone"] = "Zone";
    LocationType["Rack"] = "Rack";
    LocationType["Shelf"] = "Shelf";
    LocationType["Bin"] = "Bin";
})(LocationType || (LocationType = {}));
// Đ<PERSON>nh nghĩa kiểu cho Trạng thái
var LocationStatus;
(function (LocationStatus) {
    LocationStatus["Active"] = "Active";
    LocationStatus["Inactive"] = "Inactive";
    LocationStatus["Reserved"] = "Reserved";
})(LocationStatus || (LocationStatus = {}));
//# sourceMappingURL=warehouse_location.js.map