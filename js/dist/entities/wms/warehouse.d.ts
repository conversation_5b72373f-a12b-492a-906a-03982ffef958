import { Place } from '@type/utils/address';
/**
 * Giới hạn quyền tạo đơn hàng theo cấu hình kho và nhân viên
* Nhằm đáp ứng nhu cầu vận hành của các nhà bán hàng trong việc quản lý nhân viên,
* hiện tại khi thực hiện tạo đơn nhà bán hàng có thể giới hạn quyền cho nhân viên theo cấu hình kho.
*
* Chọn 1 kho muốn phân công cho nhân viên → tại mục Phân công bạn vui lòng nhập tên nhân viên → nhấn Lưu.
* Khi nhân viên thực hiện tạo đơn, tại bước chọn kho lấy hàng nhân viên sẽ chỉ xem được các sản phẩm
* của kho mà nhân viên đó được phân công.
* Trong trường hợp chưa đư<PERSON> cấ<PERSON> quyền, nhân viên không thể thực hiện thêm sản phẩm. Nhân viên cần gửi
* đến quản trị viên thực hiện cấp quyền.
* Với quyền chủ cửa hàng dù có được phân công hay không, khi thực hiện chọn kho tạo đơn sẽ đều
* được hiển thị tất cả các kho.
*
* Trong trường hợp đang tồn tại đơn hàng nháp, nhưng bạn chưa được cấp quyền kho, hệ thống sẽ vô hiệu hóa nút
* Tạo đơn hàng. Bạn cần thực hiện cấp quyền ngay.
* Tại mục phí vận chuyển tạm tính thì danh sách kho sẽ được hiển thị tất cả, tuy nhiên để có thể thực hiện nhấn
* vào nút Tạo đơn sau khi tính phí thì nhân viên phải được phân công ở kho đó.
* Khi các nhân viên được phân công theo kho, sẽ có 2 điều kiện để kiểm tra được đơn hàng:
* Nếu được phân quyền đọc và ghi cho "Quản lý đơn hàng" + Quyền xem "Dữ liệu đơn hàng" là Chỉ định
* ⇒ Xem và xử lý các đơn hàng mình đã tạo và các đơn hàng thuộc kho được phân công.
*
* Nếu được phân quyền đọc và ghi cho "Quản lý đơn hàng" + Quyền xem "Dữ liệu đơn hàng" là Tất cả
* ⇒ Xem và xử lý được tất cả đơn hàng của tất cả các kho.
* Nếu tại hệ thống, nhân viên không phải quyền chủ cửa hàng hoặc admin, tuy nhiên nếu được phân thêm
* nhóm quyền "Quản lý địa chỉ"  thì có thể thực hiện phân công kho.
*
*
* Logic xử lý trong ứng dụng
*
* Khi tạo đơn hàng
* Kiểm tra cấu hình cửa hàng:
* Truy vấn store_config để lấy giá trị restrict_warehouse_access.
*
* Trường hợp bật (restrictWarehouseAccess: true):
* Nếu role là owner: Hiển thị tất cả kho.
* Nếu role là staff: Chỉ hiển thị kho trong assigned_warehouses của nhân viên (từ collection staff).
* Kiểm tra đơn nháp: Nếu kho của đơn nháp không nằm trong assigned_warehouses, vô hiệu hóa nút "Tạo đơn hàng".
*
* Trường hợp tắt (restrictWarehouseAccess: false):
* Tất cả nhân viên (bao gồm staff) có thể xem và chọn tất cả kho trong warehouses khi tạo đơn, không cần kiểm tra assigned_warehouses.
* Đơn nháp luôn cho phép tạo đơn bất kể kho nào được chọn.
*
*
* Khi xem và xử lý đơn hàng
* Kiểm tra cấu hình:
* Truy vấn restrictWarehouseAccess từ store_config.
*
* Trường hợp bật (true):
* Nếu order_data_view là assigned: Chỉ hiển thị đơn hàng do nhân viên tạo hoặc thuộc kho được phân công.
* Nếu order_data_view là all: Hiển thị tất cả đơn hàng.
*
* Trường hợp tắt (false):
* Bỏ qua assigned_warehouses, hiển thị tất cả đơn hàng cho nhân viên có quyền order_management (đọc/ghi), tùy thuộc vào order_data_view.
* Khi tính phí vận chuyển tạm tính
* Bật: Chỉ cho phép tạo đơn nếu kho được chọn nằm trong assigned_warehouses của nhân viên (trừ chủ cửa hàng).
* Tắt: Cho phép chọn bất kỳ kho nào và tạo đơn mà không cần kiểm tra phân công.
 */
/**
 * Interface mô tả thông tin tổng quan của một kho hàng.
 * Đây là cấp cao nhất trong hệ thống phân cấp kho.
 */
export interface Warehouse {
    _id: string;
    companyId: string;
    storeId: string;
    branchId: string;
    name: string;
    address: Place;
    capacity: number;
    assignedStaff: Array<{
        userId: string;
        name: string;
    }>;
    isDefault: boolean;
}
/**
 * Logic:
 * - `floors` là mảng tham chiếu đến các tầng trong kho (Collection Floor).
 * - Khi truy vấn kho, có thể dùng `$lookup` trong MongoDB để lấy thông tin chi tiết của floors.
 * - Ví dụ: Lấy danh sách tầng: db.warehouses.aggregate([{ $lookup: { from: "floors", localField: "floors._id", foreignField: "_id", as: "floorDetails" } }])
 */
export interface WarehouseLocation {
    warehouseId: string;
    locationId: string;
}
