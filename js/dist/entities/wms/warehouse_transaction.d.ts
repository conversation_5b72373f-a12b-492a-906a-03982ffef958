/**
 * bin_transactions theo dõi các giao dịch xuất nhập sản phẩm ở cấp độ vị trí cụ thể trong kho (ô - bin).
 * Collection này ghi nhận lịch sử xếp/lấy sản phẩm tại các ô trong kho.
 * https://manual.nhanh.vn/pos/kho-hang/quan-ly-vi-tri-san-pham-trong-kho/hoa-don-xuat-nhap-vi-tri
 */
export interface WarehouseTransaction {
    _id: string;
    warehouseId: string;
    binId: string;
    productId: string;
    variantId?: string;
    batchId?: string;
    quantity: number;
    transactionType: 'RECEIVE' | 'PICK' | 'ADJUST';
    createdAt: Date;
    createdBy: {
        _id: string;
        name: string;
    };
    relatedTransactionId?: string;
    orderId?: string;
}
/**
 * Logic:
 * - Khi xếp sản phẩm vào ô (RECEIVE): tạo bản ghi với quantity > 0, liên kết với stock_in từ InventoryTransaction qua relatedTransactionId.
 * - <PERSON>hi lấy sản phẩm khỏi ô (PICK): tạo bản ghi với quantity < 0, liên kết với stock_out hoặc combo_stock_out.
 * - Khi điều chỉnh (ADJUST): dùng để sửa lỗi vị trí trong kho.
 * - Ví dụ: Truy vấn lịch sử ô: db.bin_transactions.find({ binId: "bin123" }).sort({ createdAt: -1 })
 */
