"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Logic:
 * - <PERSON><PERSON> xếp sản phẩm vào ô (RECEIVE): tạo bản ghi với quantity > 0, liên kết với stock_in từ InventoryTransaction qua relatedTransactionId.
 * - <PERSON>hi lấy sản phẩm khỏi ô (PICK): tạo bản ghi với quantity < 0, liên kết với stock_out hoặc combo_stock_out.
 * - Khi điều chỉnh (ADJUST): dùng để sửa lỗi vị trí trong kho.
 * - Ví dụ: <PERSON><PERSON><PERSON> vấn lịch sử ô: db.bin_transactions.find({ binId: "bin123" }).sort({ createdAt: -1 })
 */
//# sourceMappingURL=warehouse_transaction.js.map