declare enum LocationType {
    Warehouse = "Warehouse",// Kho tổng
    Zone = "Zone",// <PERSON>hu vực
    Rack = "Rack",// <PERSON><PERSON>
    Shelf = "Shelf",// Tầng/Giá
    Bin = "Bin"
}
declare enum LocationStatus {
    Active = "Active",// <PERSON>ang hoạt động
    Inactive = "Inactive",// Không hoạt động
    Reserved = "Reserved"
}
interface Dimensions {
    length: number;
    width: number;
    height: number;
}
export interface EmbeddedWarehouseLocation {
    _id: string;
    warehouseId: string;
    name: string;
    code: string;
    type: LocationType;
    parentId: string | null;
    level: number;
}
export interface WarehouseLocation extends EmbeddedWarehouseLocation {
    companyId: string;
    storeId: string;
    branchId: string;
    capacity: number;
    dimensions: Dimensions;
    status: LocationStatus;
    createdAt: Date;
    updatedAt: Date;
}
export {};
