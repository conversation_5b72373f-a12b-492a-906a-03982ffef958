export type ReturnPurchasedOrder = {
    _id: string;
    purchasedOrderId: string;
    supplier: {
        name: string;
        _id: string;
    };
    warehouseId: string;
    status: 'completed' | 'draft';
    totalRefundAmount: number;
    paymentRefund: {
        totalRefunded: number;
        paymentMethods: Array<{
            method: 'cash' | 'bank_transfer';
            amount: number;
            cashbookEntryId: string;
        }>;
        remainingDebtReduction: number;
    };
    returnedItems: Array<{
        product: {
            _id: string;
            name: string;
        };
        /**
         * <PERSON><PERSON> nhập hàng, mỗi sản phẩm sẽ có thể có nhiều lô hàng
         * với ngày sản xuất (NSX), hạn sử dụng (HSD) khác nhau.
         * Do đó, PurchaseOrder cần liên kết với product_batches để:
         * Ghi nhận thông tin nhập hàng theo từng lô.
         * <PERSON> dõi công nợ nhà cung cấp.
         * Cập nhật tồn kho ch<PERSON>h xác.
         * Hỗ trợ quản lý hàng hóa có hạn sử dụng (mỹ phẩm, thự<PERSON> phẩm, d<PERSON><PERSON><PERSON> phẩm...).
         */
        batchId?: string;
        quantity: number;
        unitPrice: number;
        subTotal: number;
        warehouseTransactionId: string;
    }>;
    relatedDocuments: Array<{
        documentType: 'refund_invoice';
        documentId: string;
    }>;
    createdBy: {
        _id: string;
        name: string;
    };
    shift: {
        _id: string;
        shiftId: number;
    };
    returnDate: Date;
    createdAt: Date;
    updatedAt: Date;
};
