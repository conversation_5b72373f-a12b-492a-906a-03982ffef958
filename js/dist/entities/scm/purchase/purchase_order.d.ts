import { Time } from '@type/utils/common';
import { SimplifyProduct } from '@entities/ims/product/product';
import { PosPurchasedItem } from './purchased.pos';
export type PurchasedInvoiceItem = PosPurchasedItem & {
    product: SimplifyProduct;
    variantId: string;
    images?: string[];
    priceChange?: number;
    priceChangePct?: number;
    subtotal: number;
    inventoryTransactionId: string;
    /**
     * <PERSON><PERSON> nhập hàng, mỗi sản phẩm sẽ có thể có nhiều lô hàng
     * với ngày sản xuất (NSX), hạn sử dụng (HSD) khác nhau.
     * Do đó, PurchaseOrder cần liên kết với product_batches để:
     * Ghi nhận thông tin nhập hàng theo từng lô.
     * Theo dõi công nợ nhà cung cấp.
     * Cập nhật tồn kho chính xác.
     * Hỗ trợ quản lý hàng hóa có hạn sử dụng (mỹ phẩm, thực phẩm, dược phẩm...).
     */
    batchId?: string;
};
export interface PurchaseOrder {
    companyId: string;
    storeId: string;
    branchId: string;
    purchaseId: string;
    supplier: {
        name: string;
        _id: string;
    };
    /**
     * pay lúc đầu, có thể pay 1 phần bằng cash, 1 phần bằng bank
     * nên vẫn fai array
     */
    initPayments: Array<PurchasedPayment>;
    laterPayments?: Array<PurchasedPayment>;
    summary: {
        total: number;
        totalDiscount: number;
        discountRatio: number;
        subTotal: number;
        totalQuantity: number;
        totalItems: number;
    };
    /**
     * phiếu công nợ
     */
    debtReceiptImages?: string[];
    finalizedPayment: {
        paidAmount: number;
        debt: number;
    };
    items: Array<PurchasedInvoiceItem>;
    relatedDocuments: Array<{
        documentType: 'invoice' | 'debt_payment';
        documentId: string;
    }>;
    status: 'cancelled' | 'completed' | 'draft';
    refReceiptIds?: string[];
    refBankPaymentReceiptId?: string;
    purchasedAt: Date;
    shift: {
        _id: string;
        shiftId: number;
    };
    time: Time;
    logs?: string[];
    kiotvietData?: any;
}
export type PurchasedPayment = {
    refReceiptId: string;
    cashbookEntryId: string;
    paidAmount: number;
    debt: number;
    paidAt: Date;
    status: 'completed' | 'cancelled';
    paymentMethod: 'cash' | 'bank_transfer' | 'credit_card';
};
export type PurchasedInvoiceLaterPayment = {
    refReceiptId: string;
    paidAmount: number;
    debt: number;
    paidAt: Date;
    status: 'completed' | 'cancelled';
    paymentMethod: 'cash' | 'bank_transfer';
};
