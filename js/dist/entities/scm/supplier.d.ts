import { Place } from "@type/utils/address";
export type BranchSupplier = {
    _id?: string;
    companyId: string;
    storeId: string;
    name: string;
    /**
     * ví dụ tên NCC là thịt lợn hằng nga
     * tên người liên hệ là a Trung
     */
    contactName?: string;
    phoneNumber?: string;
    email?: string;
    company?: string;
    /**
     * mã số thuế
     */
    tin?: string;
    /**
     * số tài khoản
     */
    bank?: {
        accountNumber?: string;
        bankName?: string;
        accountName?: string;
    };
    address?: Place;
    /**
     * default ko có nhà cung cấp
     * ko thể thay đổi
     */
    immutable?: boolean;
};
export interface Supplier extends BranchSupplier {
    branchId: string;
    status: 'active' | 'inactive';
    /**
     * aggregate
     */
    totalPurchasedAmount?: number;
    totalDebt?: number;
}
