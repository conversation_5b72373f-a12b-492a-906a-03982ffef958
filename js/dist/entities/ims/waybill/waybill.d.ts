/**
 * Quản lý vận đơn
 *
 * 🔹 1. <PERSON><PERSON><PERSON> bán thiết lập phí vận chuyển
 * Nếu tính phí theo giá trị đơn hàng → shipping_rates.
 * Nếu tính phí theo khu vực → shipping_zones.
 * Nếu dùng bảng giá nhà vận chuyển → shipping_providers.
 *
 * 🔹 2. <PERSON>hi tạo đơn hàng (orders)
 * Kiểm tra mức phí vận chuyển phù hợp.
 * Nếu đơn hàng đủ điều kiện miễn phí → shippingFee = 0.
 * Nếu áp dụng phí xử lý → Lấy từ handling_fees.
 *
 * 🔹 3. Khi đơn hàng được xác nhận, tạo vận đơn (shipments)
 * Chọn nhà vận chuyển (shipping_providers).
 * Áp dụng gói đóng hàng từ shipping_packages.
 * Đồng bộ trạng thái giao hàng từ API nhà vận chuyển.
 */
/**
 * <PERSON>ì Sao Nên Tách shipments Ra Riêng, không dùng chung vào collection Order
 *
 * Linh hoạt khi có nhiều vận đơn trên một đơn hàng:
 * - Một đơn hàng có thể chia thành nhiều kiện hàng (shipments) nếu sản phẩm được gửi từ nhiều kho hoặc qua nhiều nhà vận chuyển.
 * - Ví dụ: Một đơn hàng lớn có thể chia thành 2 kiện hàng, một gửi qua GHN, một gửi qua Viettel Post.
 *
 * Quản lý trạng thái vận đơn dễ dàng:
 * - Trạng thái giao hàng thay đổi theo thời gian (ví dụ: pending → picked → shipping → delivered → failed).
 * - Khi có một collection riêng, ta có thể cập nhật trạng thái dễ dàng mà không cần cập nhật toàn bộ order.
 *
 * Tích hợp với nhiều đơn vị vận chuyển:
 * - Nếu bạn tích hợp API của GHN, Ahamove, J&T..., các đơn vị này thường có mã vận đơn (trackingNumber) riêng.
 * - Tách shipments giúp lưu và đồng bộ trạng thái từ nhiều API vận chuyển mà không làm phức tạp orders.
 *
 * Xử lý hoàn hàng hoặc giao lại đơn hàng:
 * - Nếu đơn hàng bị trả về hoặc cần giao lại, bạn có thể tạo một shipment mới mà không phải sửa toàn bộ orders.
 *
 * Giúp đơn hàng gọn hơn, dễ đọc, dễ truy vấn:
 * - Khi đơn hàng chỉ lưu thông tin cơ bản, dữ liệu nhẹ hơn, dễ quản lý.
 * - Khi cần chi tiết vận đơn, chỉ cần truy vấn shipments thay vì tải toàn bộ orders.
 */
export interface Waybill {
    '_id': string;
    carrierId: string;
    'storeId': string;
    branchId: string;
    'orderId': string;
    'shipmentId': string;
    'status': 'shipped' | 'pending' | 'failed' | 'delivered' | 'cancelled' | 'returned' | 'on_the_way' | 'pickup_ready' | 'pickup_done';
    'trackingNumber': string;
    'package': {
        'packageId': string;
        'weight': number;
    };
    'createdAt': Date;
}
