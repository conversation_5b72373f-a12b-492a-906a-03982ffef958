"use strict";
/**
 * <PERSON><PERSON><PERSON>n lý vận đơn
 *
 * 🔹 1. <PERSON><PERSON><PERSON> bán thiết lập phí vận chuyển
 * Nếu tính phí theo giá trị đơn hàng → shipping_rates.
 * Nếu tính phí theo khu vực → shipping_zones.
 * Nếu dùng bảng giá nhà vận chuyển → shipping_providers.
 *
 * 🔹 2. <PERSON>hi tạo đơn hàng (orders)
 * Kiểm tra mức phí vận chuyển phù hợp.
 * Nếu đơn hàng đủ điều kiện miễn phí → shippingFee = 0.
 * Nếu áp dụng phí xử lý → Lấy từ handling_fees.
 *
 * 🔹 3. Khi đơn hàng được xác nhận, tạo vận đơn (shipments)
 * Chọn nhà vận chuyển (shipping_providers).
 * Áp dụng gói đóng hàng từ shipping_packages.
 * Đồng bộ trạng thái giao hàng từ API nhà vận chuyển.
 */
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=waybill.js.map