/**
 * Thi<PERSON><PERSON> lập phí vận chuyển tùy chỉnh.
 */
export interface ShippingRateConfiguration {
    '_id': string;
    companyId: string;
    'storeId': string;
    branchId: string;
    /**
     * Loại phí vận chuyển
     * - value_based: Phí vận chuyển dựa trên giá trị hàng hóa
     * - weight_based: Phí vận chuyển dựa trên trọng lượng hàng hóa
     * - volume_based: Phí vận chuyển dựa trên thể tích hàng hóa
     * - distance_based: Phí vận chuyển dựa trên khoảng cách
     */
    'type': 'value_based' | 'weight_based' | 'volume_based' | 'distance_based';
    'region': {
        'country': string;
        'province': string;
        'district': string;
    };
    /**
     * các đơn vị vận chuyển tính phí theo khoảng cách (km),
     */
    carrier: {
        'name': string;
        'apiUrl': string;
    };
    shippingZoneId: string;
    'ranges': Array<{
        'minDistance': number;
        'maxDistance': number;
        'pricePerKm': number;
        'applyFromStart': boolean;
        /**
         * Nếu đơn vị vận chuyển tính phí theo cả quãng đường (km) và cân nặng (kg)
         */
        'weightTiers': Array<{
            'minWeight': number;
            'maxWeight': number;
            'extraPerKg': number;
        }>;
    }>;
    surcharges: Array<{
        '_id': string;
        'description': string;
        'amount': number;
    }>;
    /**
     * Việc phải trả thêm chi phí vận chuyển khiến Khách mua hàng khó ra quyết định mua.
     * Vậy nên việc cung cấp các tùy chọn vận chuyển miễn phí có thể đem lại hiệu quả cho các chiến lược bán hàng.
     * Việc giảm hoặc miễn phí vận chuyển có thể ảnh hưởng đến doanh thu của Nhà bán hàng. Tuy nhiên,
     * Nhà bán hàng có thể khắc phục bằng nhiều cách. Chẳng hạn như tăng giá sản phẩm
     * ( tính cả chi phí vận chuyển vào giá sản phẩm), hoặc áp dụng vận chuyển miễn phí khi đơn hàng
     * đáp ứng đủ lợi nhuận mong muốn.
     */
    'rules': Array<{
        'minValue': number;
        'maxValue': number;
        'price': number;
        'freeShipping': boolean;
    }>;
}
