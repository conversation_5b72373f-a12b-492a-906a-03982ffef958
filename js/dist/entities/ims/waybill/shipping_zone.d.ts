/**
 * Thiết lập phí vận chuyển theo khu vực
 *
 * M<PERSON>t số cửa hàng có ship ruột, hoặc một số đơn vị như GHN, GHTK, họ chỉ cần biết
 * giao từ khu vực nào, giao đến khu vực nào
 * là ra giá bao nhiêu, không tính theo km
 *
 * Một quốc gia hoặc tỉnh thành phố có thể thiết lập nhiều mức phí khác nhau.
 * Nhà bán có thể thiết lập tính phí dựa trên khối lượng khối lượng sản phẩm hoặc tổng giá trị đơn đơn hàng.
 */
export interface ShippingZone {
    '_id': string;
    companyId: string;
    'storeId': string;
    branchId: string;
    carrier: {
        'name': string;
        'apiUrl': string;
    };
    'country': string;
    'province': string;
    'district': string;
    'pricing': Array<{
        'type': 'weight_based' | 'order_value_based';
        'minValue': number;
        'maxValue': number;
        'price': number;
    }>;
}
