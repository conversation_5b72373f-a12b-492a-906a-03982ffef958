/**
 * inventory_adjustments theo dõi các điều chỉnh tồn kho.
 * Đ<PERSON><PERSON> là collection riêng biệt để theo dõi các trường hợp điều chỉnh tồn kho khi có sự khác biệt giữa
 * số lượng thực tế và lý thuyết (do sai sót, mất mát, hư hỏng, v.v.).
 */
export interface InventoryAdjustment {
    _id: string;
    companyId: string;
    storeId: string;
    branchId: string;
    productId: string;
    variantId: string;
    warehouseId: string;
    adjustmentType: 'loss' | 'gain';
    adjustmentQuantity: number;
    reason: string;
    adjustmentDate: Date;
    createdAt: Date;
    createdBy: {
        _id: string;
        name: string;
    };
}
