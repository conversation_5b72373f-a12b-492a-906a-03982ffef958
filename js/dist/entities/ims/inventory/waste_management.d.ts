/**
 * <PERSON><PERSON> Nào Cần Tạo waste_management?
 *  Hàng hết hạn sử dụng không thể bán được.
 *  Hàng bị hỏng do lưu kho hoặc vận chuyển.
 *  Hàng bị lỗi sản xuất mà không thể trả nhà cung cấp.
 *  Hàng bị ảnh hưởng do thiên tai (mốc, ẩm, mối mọt).
 *
 * <PERSON>hi hàng hóa bị hỏng, lỗi hoặc hết hạn mà không thể đổi trả hoặc thanh lý, cần có quy trình xuất hủy để:
 *  Gi<PERSON><PERSON> số lượng trong kho (inventory)
 *  Cập nhật sổ quỹ (cashbook) nếu có chi phí tiêu hủy
 *  Ghi nhận kế toán (accounting_documents) để hợp lệ về thuế
 *  Tạo báo cáo hủy hàng (waste_management)
 */
export interface WasteManagement {
    _id: string;
    warehouseId: string;
    companyId: string;
    storeId: string;
    branchId: string;
    wasteType: 'discard' | 'reuse';
    reason: Date;
    wasteItems: Array<{
        productId: string;
        variantId: string;
        quantity: number;
        unitCost: number;
        totalCost: number;
    }>;
    totalWasteCost: number;
    disposalMethod: string;
    status: 'completed' | 'draft';
    createdBy: {
        _id: string;
        name: string;
    };
    wasteDate: Date;
}
