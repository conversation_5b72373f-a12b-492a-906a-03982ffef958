import { ImportAdditionalCost } from "@entities/scm/import_additional_cost";
import { EmbeddedProduct } from "../product/embedded_product";
import { EmbeddedWarehouseLocation } from "@entities/wms/warehouse_location";
import { EmbeddedProductBatch } from "../product/product_batch";
export interface ProductBatchItem extends EmbeddedProductBatch {
    quantity: number;
}
export interface GoodsReceiptItem {
    _id: string;
    product: EmbeddedProduct;
    quantityOrdered?: number;
    quantityReceived: number;
    quantityAccepted?: number;
    price: number;
    subTotal: number;
    discount?: number;
    total: number;
    allocatedCosts?: {
        _id: string;
        amount: number;
    }[];
    inventoryTransactionId: string;
    warehouseLocation?: EmbeddedWarehouseLocation;
    images?: string[];
    note?: string;
    /**
     * Danh sách lô hàng của sản phẩm
     */
    batches?: ProductBatchItem[];
}
export interface TransportInfo {
    transportMethod?: 'road' | 'sea' | 'air' | 'other';
    carrier?: string;
    trackingNumber?: string;
    estimatedDeliveryDate?: Date;
    actualDeliveryDate?: Date;
}
export interface QualityCheckRejectedItem {
    _id: string;
    name: string;
    quantity: number;
    reason: string;
}
export interface QualityCheck {
    status: 'pending' | 'passed' | 'failed' | 'partial';
    checkedBy?: {
        _id: string;
        name: string;
    };
    checkedAt?: Date;
    notes?: string;
    rejectedItems?: Array<QualityCheckRejectedItem>;
}
export interface InitialPayment {
    _id?: string;
    amount: number;
    method: 'cash' | 'bank_transfer' | 'credit_card';
    paidAt: Date;
    cashbookEntryId?: string;
    bankAccountId?: string;
    note?: string;
}
export interface TaxInfo {
    type: 'VAT' | 'import_tax' | 'other';
    rate: number;
    amount: number;
}
export interface GoodsReceipt {
    _id: string;
    receiptId?: string;
    companyId: string;
    storeId: string;
    branchId: string;
    refPurchaseOrderId?: string;
    supplier: {
        _id: string;
        name: string;
    };
    items: GoodsReceiptItem[];
    additionalCosts?: ImportAdditionalCost[];
    transportInfo?: TransportInfo;
    qualityCheck?: QualityCheck;
    taxes?: TaxInfo[];
    summary: {
        subTotal: number;
        totalDiscount?: number;
        totalSupplierAdditionalCost?: number;
        totalNonSupplierAdditionalCost?: number;
        totalAdditionalCost?: number;
        totalTax?: number;
        total: number;
        totalQuantity: number;
        totalItems: number;
    };
    payment: {
        debt?: {
            debtAmount: number;
            dueDate?: Date;
            debtReceiptImages?: string[];
        };
        initialPayments?: InitialPayment[];
    };
    status: 'draft' | 'received' | 'checked' | 'completed' | 'cancelled';
    createdBy: {
        _id: string;
        name: string;
    };
    receivedAt: Date;
    updatedAt?: Date;
    note?: string;
    logs?: {
        action: string;
        user: {
            _id: string;
            name: string;
        };
        timestamp: Date;
        details?: string;
    }[];
}
