import { EmbeddedWarehouseLocation } from "@entities/wms/warehouse_location";
import { EmbeddedProduct } from "../product/embedded_product";
import { ProductUnit } from "../product/product_variant";
import { EmbeddedProductBatch } from "../product/product_batch";
import { EmbeddedProductSerial } from "../product/product_serial";
export interface InventoryCheckItemUnit {
    unit: ProductUnit;
    stockQuantity: number;
    actualQuantity?: number;
}
export interface InventoryCheckItemBatch {
    batch: EmbeddedProductBatch;
    stockQuantity: number;
    actualQuantity?: number;
    differenceQuantity: number;
    differenceValue: number;
}
export interface InventoryCheckItem {
    product: EmbeddedProduct;
    warehouseLocation?: EmbeddedWarehouseLocation;
    stockQuantity: number;
    actualQuantity?: number;
    differenceQuantity: number;
    differenceValue: number;
    /**
     * chi tiết số lượng tồn kho và thực tế theo từng đơn vị tính (unit of measure - UoM)
     */
    unitDetails?: Array<InventoryCheckItemUnit>;
    batchDetails?: Array<InventoryCheckItemBatch>;
    serialDetails?: Array<EmbeddedProductSerial>;
    note?: string;
}
export interface InventoryCheckSummary {
    totalIncrease: {
        quantity: number;
        value: number;
    };
    totalDecrease: {
        quantity: number;
        value: number;
    };
    totalDifference: {
        quantity: number;
        value: number;
    };
}
export interface InventoryCheck {
    _id: string;
    warehouse: {
        _id: string;
        name: string;
    };
    items: Array<InventoryCheckItem>;
    summary: InventoryCheckSummary;
    note?: string;
    status: 'draft' | 'in_progress' | 'pending_approval' | 'approved' | 'completed' | 'rejected' | 'cancelled';
    adjustmentReason: {
        reasonId: string;
        description?: string;
    };
    /**
     * Trong các hệ thống ERP, kiểm kho thường liên quan đến các tài liệu khác như phiếu nhập kho,
     * phiếu xuất kho, hoặc đơn hàng. Trường này giúp liên kết với các giao dịch khác.
     */
    references?: {
        documentType: string;
        documentId: string;
    }[];
    /**
     * Kiểm kho có thể bắt nguồn từ các nguồn khác nhau
     * (ví dụ: kiểm định kỳ, kiểm do sai lệch, kiểm theo yêu cầu khách hàng).
     * SAP và Odoo thường lưu thông tin này.
     */
    source?: string;
    /**
     * thông tin kiểm toán (audit trail)
     */
    createdBy: {
        _id: string;
        name: string;
    };
    createdAt: Date;
    updatedBy?: {
        _id: string;
        name: string;
    };
    updatedAt?: Date;
    approvedBy?: {
        _id: string;
        name: string;
    };
    approvedAt?: Date;
}
