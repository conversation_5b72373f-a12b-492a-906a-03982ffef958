import { WarehouseLocation } from '@entities/wms/warehouse';
/**
 * inventory_transactions theo dõi các giao dịch thay đổi số lượng kho (nhập/xuất).
 * Collection này sẽ lưu trữ tất cả các giao dịch thay đổi số lượng hàng trong kho,
 * bao gồm nhập kho, xuất kho, và điều chỉnh tồn kho.
 */
export interface InventoryTransaction {
    _id: string;
    companyId: string;
    storeId: string;
    branchId: string;
    productId: string;
    variantId: string;
    /**
     * <PERSON><PERSON> nhập hàng, mỗi sản phẩm sẽ có thể có nhiều lô hàng
     * với ngày sản xu<PERSON> (NSX), hạn sử dụng (HSD) khác nhau.
     * Do đó, PurchaseOrder cần liên kết với product_batches để:
     * <PERSON><PERSON> nhận thông tin nhập hàng theo từng lô.
     * <PERSON> dõi công nợ nhà cung cấp.
     * Cậ<PERSON> nhật tồn kho chính xác.
     * Hỗ trợ quản lý hàng hóa có hạn sử dụng (mỹ phẩm, thực phẩm, dược phẩm...).
     */
    batchId?: string;
    /**
     * Thông tin vị trí chi tiết trong kho (ô - bin).
     * Không bắt buộc, chỉ ghi nhận khi giao dịch liên quan đến vị trí cụ thể (như xếp vào ô hoặc lấy từ ô).
     */
    location?: WarehouseLocation;
    /**
     * waste_out = xuất hủy
     */
    transactionType: 'stock_in' | 'stock_out' | 'restock' | 'adjustment' | 'waste_out' | 'combo_stock_out';
    quantity: number;
    comboItems: Array<{
        productId: string;
        variantId: string;
        quantity: number;
        binId?: string;
    }>;
    transactionDate: Date;
    /**
     * Liên kết với đơn hàng hoặc giao dịch khác (nếu có).
     * Ví dụ: orderId khi stock_out cho đơn hàng bán.
     */
    relatedId?: string;
    createdAt: Date;
    createdBy: {
        _id: string;
        name: string;
    };
    note: string;
}
