/**
 * inventory lưu trữ số lượng hàng hiện tại
 * Collection này sẽ lưu trữ thông tin chi tiết về từng sản phẩm trong kho,
 * bao gồm số lượng tồ<PERSON> kho, số lượng đã bán, và các thông tin bổ sung như giá trị tồn kho.
 */
export interface Inventory {
    _id: string;
    companyId: string;
    storeId: string;
    branchId: string;
    productId: string;
    variantId: string;
    warehouseId: string;
    quantityInStock: number;
    quantitySold: number;
    reorderLevel: number;
    createdAt: Date;
    updatedAt: Date;
}
