import { EmbeddedProductBatch } from "./product_batch";
import { ProductSimpleVariant, ProductUnit } from "./product_variant";
/**
 * interface để nhúng vào các object khác như Order, Purchase...
 */
export type EmbeddedProduct = {
    /**
      * không dùng _id để phân biệt rõ ràng với variantId
      */
    productId: string;
    sku: string;
    barCode?: string;
    cost: number;
    name: string;
    price: number;
    linkedModifierGroupIds?: string[];
    /**
     * néu chọn variant
     */
    variant?: ProductSimpleVariant;
    /**
     * néu chọn unit
     */
    unit?: ProductUnit;
    /**
     * nếu chọn lô hàng
     */
    batch?: EmbeddedProductBatch;
};
