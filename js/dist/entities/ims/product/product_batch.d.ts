/**
 * quản lý sản phẩm theo lô - hạn sử dụng,
 * T<PERSON><PERSON> năng  hỗ trợ cho nhu cầu quản lý tồn kho chuyên sâu theo lô
 * và hạn sử dụng cho các nhà bán hàng ngành mỹ phẩm, thự<PERSON>h<PERSON>, d<PERSON><PERSON><PERSON>h<PERSON>,...
 * giúp nhà bán hàng đảm bảo luôn cung cấp mặt hàng mới,
 * đảm bảo chất lượng ra thị trường.
 *
 * https://support.haravan.com/support/solutions/articles/***********-h%C6%B0%E1%BB%9Bng-d%E1%BA%ABn-qu%E1%BA%A3n-l%C3%BD-s%E1%BA%A3n-ph%E1%BA%A9m-theo-l%C3%B4-h%E1%BA%A1n-s%E1%BB%AD-d%E1%BB%A5ng
 * https://manual.nhanh.vn/pos/san-pham-v2/danh-sach-san-pham-v2/y-nghia-cac-loai-sp-v2/san-pham-theo-lo-v2
 */
import { WarehouseLocation } from '@entities/wms/warehouse';
export type EmbeddedProductBatch = {
    _id: string;
    batchCode: string;
    manufactureDate: Date;
    expiryDate: Date;
};
export type ProductBatch = EmbeddedProductBatch & {
    _id: string;
    productId: string;
    variantId: string;
    quantity: number;
    locations?: WarehouseLocation[];
    note: string;
    status: 'active' | 'expired' | 'sold_out';
};
/**
 * sales_orders Collection (Áp dụng FIFO/FEFO khi bán hàng)
Khi bán hàng, hệ thống sẽ tự động lấy sản phẩm từ lô cũ nhất còn hàng hoặc lô sắp hết hạn.

📌 Bán hàng theo FIFO (First In First Out)
db.product_batches.find({
  productId: "prod_001",
  quantity: { $gt: 0 }
}).sort({ manufactureDate: 1 }).limit(1);
✅ Chọn lô nhập sớm nhất để xuất bán.

📌 Bán hàng theo FEFO (First Expired First Out)

db.product_batches.find({
  productId: "prod_001",
  quantity: { $gt: 0 }
}).sort({ expiryDate: 1 }).limit(1);
✅ Chọn lô gần hết hạn nhất để bán trước.
 */
