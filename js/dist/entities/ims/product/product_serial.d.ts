import { WarehouseLocation } from "@entities/wms/warehouse";
import { EmbeddedProductBatch } from "./product_batch";
export type EmbeddedProductSerial = {
    _id: string;
    deviceType?: 'mobile' | 'tablet' | 'smartwatch' | 'other';
    serialNumber: string;
    imei?: string;
    status: 'in_stock' | 'assigned' | 'sold' | 'missing' | 'damaged' | 'returned' | 'in_transit';
};
export interface SerialNumber {
    productId: string;
    batch?: EmbeddedProductBatch;
    createdAt: Date;
    updatedAt: Date;
    warehouseLocation: WarehouseLocation;
    manufactureDate?: Date;
    warrantyEndDate?: Date;
    lastScannedAt?: Date;
    lastScannedLocation?: string;
    notes?: string;
}
