import { WarehouseLocation } from '@entities/wms/warehouse';
/**
 * <PERSON>ản phẩm có nhiều đơn vị tính
 *Sản phẩm nhiều đơn vị tính là là 1 sản phẩm có thể được bán theo 2 hay nhiều loại đơn vị quy đổi khác nhau.
 (Ví dụ: <PERSON>hu<PERSON><PERSON> kháng sinh có thể bán theo viên, theo vỉ, theo hộp hoặc theo thùng).
Đặc điểm của sản phẩm nhiều đơn vị tính:
Gi<PERSON> bán theo mỗi đơn vị tính là khác nhau.
Mỗi đơn vị tính bán ra = đơn vị cơ bản x số lượng quy đổi.
Việc tạo sản phẩm nhiều đơn vị tính có thể giúp doanh nghiệp như sau:
<PERSON>h<PERSON><PERSON> cần tạo nhiều sản phẩm khác nhau theo từng đơn vị, dễ dàng quản lý tồn kho.
Kiểm soát được việc nhập/bán hàng từng đơn vị sản phẩm theo từng lần khác nhau.
Kiểm soát được doanh thu theo từng sản phẩm để đưa ra kế hoạch nhập hàng phù hợp, mỗi loại đơn vị tính có giá nhập khác nhau để đo lường lợi nhuận chính xác nhất.
 * https://manual.nhanh.vn/pos/san-pham-v2/danh-sach-san-pham-v2/y-nghia-cac-loai-sp-v2/san-pham-nhieu-don-vi-tinh-v2
 */
export type ProductUnit = {
    unitName: string;
    conversionRate: number;
    isBaseUnit: boolean;
    price: number;
    cost?: number;
    barcode?: string;
};
export type ProductSimpleVariant = {
    variantId: string;
    attributes: Array<{
        name: string;
        value: string;
    }>;
};
/**
 * Với mô hình Product - Variants, mỗi biến thể (size, màu) có tồn kho riêng.
 * Khi sản phẩm có nhiều loại biến thể không cố định
 * (VD: trà sữa có topping, quần áo có size, giày có màu sắc),
 * ta cần một cách thiết kế linh hoạt.
 */
export interface ProductVariant {
    _id: string;
    product: {
        _id: string;
        name: string;
    };
    sku: string;
    price: number;
    /**
     * "attributes": [
      { "name": "size", "value": "L" },
      { "name": "đường", "value": "50%" },
      { "name": "đá", "value": "70%" },
      { "name": "topping", "value": "Trân châu đen" }
       Thời trang: [{ name: "size", value: "M", isRequired: true }, { name: "color", value: "Red", isRequired: true }]
      Trà sữa: [{ name: "đường", value: "50%", isRequired: false }, { name: "topping", value: "trân châu", isRequired: false }]
      Điện tử: [{ name: "RAM", value: "8GB", isRequired: true }]
    ]
     * haravan đang cho tối đa 3 attributes
     * mỗi variant có thể có nhiều attributes
     */
    attributes: Array<{
        name: string;
        value: string;
        isRequired: boolean;
    }>;
    units: ProductUnit[];
    baseUnit: ProductUnit;
    warehouseLocations: Array<WarehouseLocation & {
        unitName: string;
        quantity: number;
    }>;
}
