import { WarehouseLocation } from '@entities/wms/warehouse';
import { Types } from 'mongoose';
export type EcommerceChannel = {
    channelProductId: string;
    channelId: string;
    externalProductId: string | number;
    price: number;
    description?: string;
    images?: Array<{
        url: string;
    }>;
    status: 'active' | 'inactive' | 'pending' | 'error';
    lastSyncedAt?: Date;
    metadata?: Record<string, any>;
};
export type SimplifyProduct = {
    _id?: typeof Types.ObjectId;
    sku: string;
    name: string;
    price: number;
    type: 'combo' | 'single' | 'service';
    isForSale?: boolean;
    allowsSale?: boolean;
    status: 'active' | 'inactive' | 'deleted' | 'draft' | 'out_of_stock';
    locations?: Array<WarehouseLocation & {
        quantity: number;
        batchNumber?: string;
    }>;
    /**
     * Mỗi sàn mỗi giá, mỗi desc, images..
     */
    ecommerceChannels?: EcommerceChannel[];
    /**
     * combo
     */
    autoCalculatePrice: false;
    comboItems: Array<{
        productId: string;
        variantId: string;
        quantity: number;
    }>;
    stockTracking: 'auto' | 'manual' | 'batch';
    brand: {
        _id: string;
        name: string;
    };
    origin: string;
    images: Array<{
        image_id: string;
        url: string;
        alt_text: string;
    }>;
    categories: Array<{
        _id: string;
        name: string;
    }>;
    tags: Array<{
        _id: string;
        name: string;
    }>;
    /**
     * lần cuối nhập hàng
     */
    lastPurchasedAt?: Date;
    /**
     * giá nhập hoặc sau khi tính
     * theo ingredients ra giá vốn
     * Nhà hàng:
     * { _id: "pho_bo", ingredients: [{ name: "thịt bò", quantity: 0.1, unit: "kg", price: 200000, source: "Nhà cung cấp A" },
     * { name: "bánh phở", quantity: 0.2, unit: "kg" }] }
     * Sản xuất:
     * { _id: "laptop", ingredients: [{ name: "CPU", quantity: 1, unit: "cái", price: 5000000 }] }
     */
    cost: number;
    ingredients?: Array<{
        _id: string;
        name: string;
        price: number;
        quantity: number;
        total: number;
        unit: string;
        source: string;
    }>;
    /**
     * chỉ dẫn hiện ra bên dưới sản phẩm
     * trên giao diện
     *
     * ví dụ: Xôi thập cẩm, bên dưới instruction là định lượng
     */
    instruction?: string;
    description?: string;
    measureUnit?: string;
    barcode?: string;
    /**
     * Trọng lượng sản phẩm giúp nhà vận chuyển tính giá vận đơn cho đơn hàng.
     */
    weight?: number;
    dimensions?: {
        length: number;
        width: number;
        height: number;
        unit: string;
    };
    /**
     * thêm các sản phẩm topping vào sản phẩm đã chọn, load từ Product Options List.
     * Ví dụ với mô hình FnB, khi khách hàng chọn ăn phở gà, thêm topping thịt bò 10k chẳng hạn.
     */
    linkedModifierGroupIds?: string[];
    /**
     * chỉ để tính cost, ko show ra phần nguyên liệu nhập vào hàng ngày
     */
    calcCostOnly?: boolean;
    /**
     * KIỂM KHO
     */
    /**
     * có quản lý tồn kho hay không
     * mốt số mặt hàng như đồ chế biến thì không quản lý tồn kho
     */
    trackInventory: boolean;
    trackBySerial?: boolean;
    trackByBatch?: boolean;
    checkInventoryPeriodically?: {
        /**
         * bao nhiêu ngày thì kiểm tra định kỳ 1 lần
         */
        cycle: 'shift' | 'day';
        interval?: number;
    };
    manualCheckInventory?: {
        /**
         * bao nhiêu ngày thì kiểm tra 1 lần
         */
        cycle: 'shift' | 'day';
        interval?: number;
    };
    /**
     * kiểm kho tự động / liên kết kiểm kho:
     * bán ra 1 hàng thì trừ x hàng trong kho
     * ví dụ:
     *  bán xôi thập cẩm tự trừ trứng cút, hành khô...
     *  bán 1 lon nước c2 tự động trừ luôn lon c2 đấy
     *  bán 1 cái túi lv trừ 1 cái túi lv
     */
    isAutoExportInventory?: boolean;
    linkedInventoryItems?: Array<{
        _id: string;
        exportAmount: number;
        /**
         * có 2 loại:
         *  1 loại export với số lượng chắc chắn như:
         *    bán 1 cái túi lv trừ 1 cái túi lv
         *  1 loại của hàng ăn với số lượng ko chắc chắn như:
         *    bán 1 bát phở - 1 lạng thịt
         * -> thịt còn lại trong kho lại ít hơn
         *
         * để gửi cảnh báo khi một mặt hàng bị chênh lệch quá nhiều
         *
         * thịt nhập vào x số lượng nhưng thành phẩm ra lại y số lượng
         * thịt nhập vào x số lượng nhưng họ chỉ chế biến y số lượng,
         * ra thành phẩm z số lượng, tồn kho k số lượng
         */
        isExactAmount: boolean;
        /**
         * sai số cho phép trước khi gửi cảnh báo chênh lệch
         */
        permissibleError?: number;
    }>;
    inventoryNote?: string;
    suppliers?: Array<{
        _id: string;
        name: string;
    }>;
};
export interface Product extends SimplifyProduct {
    companyId: string;
    storeId: string;
    branchId: string;
    order?: number;
    /**
     * Sản phẩm cha không trực tiếp có thuộc tính hay đơn vị tính, mà chúng được gán cho từng biến thể.
     * Mỗi biến thể có thể có:
     * - Nhiều thuộc tính (bắt buộc hoặc tùy chọn) để định nghĩa đặc điểm.
     * - Nhiều đơn vị tính để quản lý giá bán và tồn kho linh hoạt.
     */
    variants?: Array<{
        _id: string;
        attributes: Array<{
            name: string;
            value: string;
        }>;
    }>;
    kiotvietData?: any;
    shopeeData?: any;
    grabData?: any;
    logs?: string[];
}
export type ThirdPartyProduct = {
    name: string;
    price: number;
    category: string;
    id: string | number;
    parentGroupId?: string | number;
    desc: string;
    imageUrls: string[];
    imageUrl: string;
};
