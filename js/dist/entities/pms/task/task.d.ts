/**
 * References:
 * - file://./../../../../docs/modules/hrm/task.md
 */
/**
 * a. <PERSON><PERSON> công công việc linh hoạt
 * Phân công cho nhóm hoặc cá nhân: Hỗ trợ gán công việc cho cả nhóm (group_id)
 * hoặc từng nhân viên cụ thể (assigned_user_ids).
 *
 * Phân công lại (Reassignment): Cho phép quản lý chuyển công việc từ nhóm này sang nhóm
 * khác hoặc từ nhân viên này sang nhân viên khác.
 *
 * b. Quản lý ưu tiên và thời hạn
 * Ưu tiên (Priority): Công việc có thể được đánh dấu mức độ quan trọng (low, medium, high) để nhân viên
 * xử lý theo thứ tự.
 * Thờ<PERSON> hạn (Deadline): Th<PERSON><PERSON> khả năng đặt deadline và cảnh báo nếu công vi<PERSON>ầ<PERSON> (cần logic ứng dụng hỗ trợ).
 *
 * c. <PERSON> dõi trạng thái chi tiết
 * Trạng thái (Status): Mở rộng trạng thái công việc để phản ánh quy trình thực tế
 * (pending → in_progress → completed/cancelled).
 *
 * Lịch sử (History): Ghi lại mọi thay đổi để dễ truy vết (ai phân công, khi nào, trạng thái thay đổi ra sao).
 *
 * d. Tự động hóa phân công
 * Phân bổ ngẫu nhiên: Tự động chọn một nhân viên trong nhóm để gán công việc (dùng logic ứng dụng).
 * Phân bổ theo tải công việc: Dựa trên số lượng công việc hiện tại của từng nhân viên trong
 * nhóm để phân phối công bằng (cần truy vấn đếm số Assignments của mỗi user_id).
 */
export interface Task {
    id: string;
    type: 'order' | 'support' | 'inventory' | 'purchase_order';
    entityId: string;
    assignedGroup: {
        _id: string;
        name: string;
    };
    assignedLeads: Array<{
        _id: string;
        name: string;
    }>;
    assignedEmployees: Array<{
        _id: string;
        name: string;
    }>;
    /**
     * các đối tượng liên quan
     * ví dụ nhập hàng: purchase_order
     * liên quan đến đối tác nhập hàng
     */
    relatedEntities: Array<{
        entityType: 'customer' | 'partner';
        entityId: string;
    }>;
    priority: 'high' | 'low' | 'medium';
    status: AssignmentStatus;
    startDate: Date;
    dueDate: Date;
    description: string;
    /**
     *
     * Phân công công việc định kỳ (Recurring Task Assignment)
     * vd: Gán nhiệm vụ "Gửi báo cáo doanh thu" cho nhân viên B, lặp lại mỗi thứ 6 hàng tuần.
     */
    recurring: {
        frequency: 'daily' | 'weekly' | 'monthly';
        startCycle: Date;
        endCycle: Date;
        repeatCount: number;
    };
    /**
     * Phân công công việc theo ca (Shift-based Task Assignment)
     * vd: Gán nhiệm vụ "Kiểm tra hàng tồn kho" cho nhân viên C, làm từ 10:00 đến 12:00 hàng ngày.
     */
    shift: {
        shiftId: string;
    };
    /**
     * Phân công công việc liên quan đến KPI (KPI-linked Task Assignment)
     * vd: Gán nhiệm vụ "Bán 10 sản phẩm" cho nhân viên D, đóng góp 20% vào KPI doanh số tháng.
     */
    kpi: {
        kpiId: string;
        contributionValue: number;
    };
    /**
     * Phân công công việc theo dự án (Project-based Task Assignment)
     * vd: Gán nhiệm vụ "Hoàn thành phần mềm CRM" cho nhân viên E, thuộc dự án "Phát triển hệ thống CRM".
     */
    project: {
        projectId: string;
        contributionValue: number;
    };
    /**
     * Phân công công việc tự động (Automated Task Assignment)
     * vd: Gán nhiệm vụ "Gửi báo cáo tổng hợp" cho nhân viên F, tự động gửi hàng ngày lúc 10:00.
     * Tự động gán "Gặp khách hàng X" cho nhân viên gần nhất dựa trên GPS.
     */
    automated: {};
    createdBy: {
        _id: string;
        name: string;
    };
    metadata: any;
    createdAt: Date;
    updatedAt: Date;
}
export type AssignmentStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';
