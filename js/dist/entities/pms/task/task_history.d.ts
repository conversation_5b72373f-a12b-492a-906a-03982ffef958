import { AssignmentStatus } from './task';
export interface TaskHistory {
    'id': string;
    'assignmentId': string;
    'action': 'assign' | 'reassign' | 'update_status' | 'complete';
    'group': {
        _id: string;
        name: string;
    };
    'user': {
        _id: string;
        name: string;
    };
    'changedBy': {
        _id: string;
        name: string;
    };
    'previousStatus': AssignmentStatus;
    'newStatus': AssignmentStatus;
    'timestamp': Date;
    'note': string;
}
