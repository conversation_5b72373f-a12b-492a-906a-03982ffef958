export interface User {
    _id: string;
    username: string;
    email: string;
    phoneNumber: string;
    password: string;
    /**
     * các loại có thể login vào hệ thống
     * system: <PERSON><PERSON> hệ thống
     * client: <PERSON><PERSON> do<PERSON> nghi<PERSON> (mặc định khi đăng ký mới)
     * employee: Nhân viên 1 doanh nghiệp
     * affiliate_partner: Đối tác affiliate vào xem kết quả doanh thu
     */
    type: 'system' | 'client' | 'employee' | 'affiliate_partner';
    /**
     * trạng thái
     * pending_verify: Ch<PERSON> xác nhận
     * new_client: Kh<PERSON>ch mới đang setup thông tin, chưa mua gói cũng chưa kích hoạt trial
     * expired: H<PERSON><PERSON> hạn hợp đồng với hệ thống ERP, trên frontend sẽ ra form gia hạn
     * active: Hoạt động
     * inactive: Tạm hoãn
     * deleted: Xóa
     */
    status: 'pending_verify' | 'expired' | 'active' | 'inactive' | 'deleted' | 'new_client';
    employeeId?: string;
    /**
     * các field dướ<PERSON> để check xem user c<PERSON> thuộc công ty/ chi nh<PERSON>h này không
     * để check cơ bản rồi mới check các nâng cao sau
     */
    branchIds?: string[];
    companyIds?: string[];
}
