import { Time } from '@type/utils/common';
/**
 * CashierShift để quản lý tiền mặt, tồn kho, và các hoạt động liên quan đến ca làm việc của thu ngân.
 * Còn UserShift để quản lý thông tin về ca làm việc của tất cả nhân viên.
 */
export interface CashierShift {
    _id: string;
    companyId: string;
    storeId: string;
    branchId: string;
    time: Time;
    startAt: Date;
    endAt?: Date;
    data: {
        cash: {
            lastShift: number;
            revenue: number;
            purchased: number;
            otherExpenses: number;
        };
    };
    form: {
        cash: {
            begin?: number;
            end?: number;
            /**
             * rút tiền từ quầy
             */
            cashRegisterWithdrawal?: number;
            /**
             * nạp tiền vào quầy
             */
            cashRegisterRefill?: number;
            beginErrorRange?: number;
            endErrorRange?: number;
        };
        startShiftNote?: string;
        endShiftNote?: string;
    };
}
