import { Coupon, OrderValueDiscountCondition } from './coupon';
/**
 * KHÁC BIỆT GIỮA PROMOTON VÀ COUPON
 *
 * Promotion:
 * Cách áp dụng: Tự động áp dụng khi đủ điều kiện.
 * Giới hạn: <PERSON><PERSON> thể áp dụng cho tất cả khách hàng hoặc nhóm khách hàng.
 * Điều kiện: Dựa trên sản phẩm, s<PERSON> lư<PERSON>, gi<PERSON> trị đơn hàng.
 * Tích hợp với CTKM khác: <PERSON><PERSON> thể hoặc không áp dụng chung với CTKM khác.
 *
 * Coupon:
 * Cách áp dụng: Khách hàng phải nhập mã khuyến mãi.
 * Giới hạn: Thường giới hạn số lần sử dụng mỗi khách hàng..
 * Điều kiện: Cần đơn hàng tối thiểu, sản phẩm cụ thể, hoặc nhóm khách hàng..
 * Tích hợp với CTKM khác: <PERSON><PERSON> thể stack với nhiều mã khác nhau..
 */
/**
 * 1. Mua 1 Tặng 1
 * Mua 1 tặng 1 là hình thức khuyến mãi phổ biến và giúp giảm hàng tồn kho nhanh chóng.
 *
 * Cách hoạt động của ứng dụng Mua 1 Tặng 1:
 *
 * - Cho phép áp dụng khuyến mãi với Tất cả sản phẩm, Sản phẩm trong danh sách, Nhóm sản phẩm trong danh sách
 * - Sản phẩm Tặng luôn nhỏ hơn giá trị sản phẩm Mua. Hệ thống sẽ sắp xếp theo giá giảm dần để lựa chọn sản phẩm Tặng sao cho tối ưu với người mua nhất.
 *
 * Ví dụ, giỏ hàng có 4 sản phẩm đang chạy Mua 1 Tặng 1
 *
 * A giá 100,000đ.
 *
 * B giá 150,000đ.
 *
 * C giá 200,000đ.
 *
 * D giá 250,000đ.
 *
 * Thì hệ thống sẽ sắp xếp và áp dụng khuyến mãi như sau:
 *
 * D giá 250,000đ.
 *
 * C giá 200,000đ => 0đ (Tặng).
 *
 * B giá 150,000đ.
 *
 * A giá 100,000đ => 0đ (Tặng).
 *
 * Tổng giá trị sau khuyến mãi là 250,000đ+150,000đ = 400,000đ.
 *
 *
 * 2. Miễn phí giao hàng
 * Các mã giảm giá "FREESHIP" hoặc "Giảm giá vận chuyển" là một trong những loại khuyến mãi được ưa chuộng hiện tại. Khách hàng thường Đối với tâm lý khách hàng, khi nói về phí vận chuyển thì hầu hết nhiều người sẽ phải đắn đo trong việc mua hàng, nhưng khi nhận được mã "FREESHIP" thì khách sẽ cảm thấy việc chỉ thanh toán cho sản phẩm và không cần trả phí ship sẽ giúp họ tiết kiệm đi rất nhiều.
 *
 * 3. Mua hàng khuyến mại với đơn hàng bằng hoặc trên mức quy định
 * Khuyến mãi dựa trên giá trị đơn hàng là một hình thức khuyến mãi phổ biến hiện nay. Mục đích của hình thức này là đánh vào tâm lý thích khuyến mãi giảm giá của khách hàng để thúc đẩy họ mua nhiều hơn.
 *
 * 4. Giảm giá khi mua 2 sản phẩm cùng loại
 * Nếu một sản phẩm đang có doanh số thấp, bạn có thể sử dụng hình thức khuyến mãi này để thúc đẩy doanh số. Ví dụ nếu muốn tăng doanh số cho sản phẩm son A'pieu True Matt Fluid, bạn có thể thiết lập chương trình khuyến mãi mua 2 sản phẩm giá với giá 260.000 VNĐ ( sản phẩm này giá 159.000 VNĐ).
 *
 * 5. Bán hàng đồng giá
 * Khi bán sản phẩm đồng giá, khách hàng sẽ cảm thấy họ có lợi khi mua sản phẩm giá cao với mức giá thấp hơn. Ví dụ như bạn thực hiện chương trình khuyến mãi đồng giá 1.000.000 VNĐ cho các sản phẩm đầm của shop nhân dịp 8 tháng 3.
 *
 * 6. Mua sản phẩm giá ưu đãi khi đơn hàng đạt mức quy định
 * Hình thức này giúp làm tăng giá trị đơn hàng, giúp đem lại lợi ích trực tiếp mà khách hàng có thể nhận thấy được. Qua đó, bạn sẽ làm gia tăng sự hài lòng của khách khi mua hàng tại shop của bạn. Ví dụ như chương trình khuyến mãi tặng túi Canvas cao cấp cho đơn hàng La Roche Posay như Thegioiskinfood đang thực hiện. Với đơn hàng dưới 300.000 VNĐ thì những sản phẩm được khuyến mãi sẽ có màu xám, khách hàng không thể thêm vào giỏ hàng.
 *
 * Nhưng với đơn hàng từ 300.000 VNĐ trở lên, khách hàng có thể chọn thêm vào giỏ hàng
 *
 * 7. Tặng coupon
 * Việc tặng coupon cho khách hàng giúp bạn giữ chân được khách hàng, gia tăng thị phần trong một thời gian ngắn, kích hoạt sự quan tâm của khách hàng dành cho nhãn hiệu.
 *
 * 8. Ưu đãi dành cho khách hàng thân thiết
 * Việc giữ chân khách hàng là một điều rất là quan trọng, nhất là đối với các khách hàng thân thiết. Việc tạo ra các ưu đãi dành cho nhóm khách hàng thân thiết này sẽ giúp cho nhóm khách hàng này cảm nhận được sự quan trọng của họ đối với nhãn hiệu, giữ vững sự hài lòng của khách.
 *
 */
export interface Promotion {
    '_id': string;
    companyId: string;
    'storeId': string;
    branchId: string;
    'name': string;
    'description': string;
    'type': 'buy_x_get_y' | 'order_value_discount' | 'fixed_price' | 'bulk_discount' | 'coupon_gift' | 'loyalty_discount';
    'conditions': OrderValueDiscountCondition | BuyXGetYCondition | FixedPriceCondition | BulkDiscountCondition | CouponGiftCondition & {
        'customerEligibility': Coupon['conditions']['customerEligibility'];
        'channelEligibility': Coupon['conditions']['channelEligibility'];
        'storeBranchEligibility': Coupon['conditions']['storeBranchEligibility'];
    };
    'usageLimit': Coupon['usageLimit'];
    /**
     *  áp dụng chung với các chương trình khuyến mãi khác
     *
     * Nếu cho phép khuyến mãi áp dụng chung với các CTKM khác, bạn có 2 lựa chọn:
     * Áp dụng với giá sản phẩm đã giảm:
     * Chương trình khuyến mãi Mua 1 Tặng 1 sẽ áp dụng chung với chương trình khuyến mãi
     * khác và sẽ chọn sản phẩm tặng dựa trên mức giá đã giảm của sản phẩm.
     *
     * Áp dụng với giá gốc sản phẩm: Chương trình khuyến mãi Mua 1 Tặng 1
     * sẽ áp dụng chung với chương trình khuyến mãi khác và sẽ chọn sản phẩm tặng dựa trên giá gốc của sản phẩm.
     */
    'stackable': {
        'allowed': boolean;
        'applyOn': 'discounted_price' | 'original_price';
    };
    /**
     * Thời gian hiệu lực, bạn chọn Giới hạn ngày và giờ áp dụng trong tuần
     * -> Chọn ngày giờ và thêm khung giờ sẽ được áp dụng khuyến mãi.
     *
     * nhất là cho flashsale
     */
    'timeRestrictions': Array<{
        'dayOfWeek': number;
        'timeSlots': Array<{
            'start': {
                hours: number;
                minutes: number;
            };
            'end': {
                hours: number;
                minutes: number;
            };
        }>;
    }>;
    'startDate': Date;
    'endDate': Date;
}
/**
 * Bán hàng đồng giá
 * Khi bán sản phẩm đồng giá, khách hàng sẽ cảm thấy họ có lợi
 * khi mua sản phẩm giá cao với mức giá thấp hơn. Ví dụ như bạn thực hiện
 * chương trình khuyến mãi đồng giá 1.000.000 VNĐ cho các sản phẩm đầm của shop nhân dịp 8 tháng 3.
 */
type FixedPriceCondition = {
    'fixedPrice': number;
    'applicableProducts': Array<{
        'productId': string;
        'variantId': string;
        'discountedPrice': number;
        'originalPrice': number;
    }>;
};
/**
 * Giảm giá khi mua 2 sản phẩm cùng loại
 * Nếu một sản phẩm đang có doanh số thấp, bạn có thể sử dụng hình thức khuyến mãi này để thúc đẩy doanh số.
 * Ví dụ nếu muốn tăng doanh số cho sản phẩm son A'pieu True Matt Fluid,
 * bạn có thể thiết lập chương trình khuyến mãi mua 2 sản phẩm giá với giá 260.000 VNĐ ( sản phẩm này giá 159.000 VNĐ).
 *
 * Mua sản phẩm giá ưu đãi khi đơn hàng đạt mức quy định
 * Hình thức này giúp làm tăng giá trị đơn hàng, giúp đem lại lợi ích trực tiếp mà khách hàng có thể nhận thấy được. Qua đó, bạn sẽ làm gia tăng sự hài lòng của khách khi mua hàng tại shop của bạn. Ví dụ như chương trình khuyến mãi tặng túi Canvas cao cấp cho đơn hàng La Roche Posay như Thegioiskinfood đang thực hiện. Với đơn hàng dưới 300.000 VNĐ thì những sản phẩm được khuyến mãi sẽ có màu xám, khách hàng không thể thêm vào giỏ hàng.
 *
 * Nhưng với đơn hàng từ 300.000 VNĐ trở lên, khách hàng có thể chọn thêm vào giỏ hàng
 *
 * Giảm giá 50K khi mua 2 sản phẩm
 * Giảm 20% khi mua 3 sản phẩm
 * Đồng giá 260K khi mua 2 son A'pieu
 */
type BulkDiscountCondition = {
    'minOrderQuantity': number;
    minOrderValue: number;
    'applicableProducts': Array<{
        'productId': string;
        'variantId': string;
        'discountedPrice': number;
        discountPercent: number;
        'originalPrice': number;
    }>;
};
/**
 * 1. Mua 1 Tặng 1
 * Mua 1 tặng 1 là hình thức khuyến mãi phổ biến và giúp giảm hàng tồn kho nhanh chóng.
 *
 * Cách hoạt động của ứng dụng Mua 1 Tặng 1:
 *
 * - Cho phép áp dụng khuyến mãi với Tất cả sản phẩm, Sản phẩm trong danh sách, Nhóm sản phẩm trong danh sách
 * - Sản phẩm Tặng luôn nhỏ hơn giá trị sản phẩm Mua. Hệ thống sẽ sắp xếp theo giá giảm dần để lựa chọn sản phẩm Tặng sao cho tối ưu với người mua nhất.
 *
 * Ví dụ, giỏ hàng có 4 sản phẩm đang chạy Mua 1 Tặng 1
 *
 * A giá 100,000đ.
 *
 * B giá 150,000đ.
 *
 * C giá 200,000đ.
 *
 * D giá 250,000đ.
 *
 * Thì hệ thống sẽ sắp xếp và áp dụng khuyến mãi như sau:
 *
 * D giá 250,000đ.
 *
 * C giá 200,000đ => 0đ (Tặng).
 *
 * B giá 150,000đ.
 *
 * A giá 100,000đ => 0đ (Tặng).
 *
 * Tổng giá trị sau khuyến mãi là 250,000đ+150,000đ = 400,000đ.
 */
type BuyXGetYCondition = {
    'minQuantity': number;
    'requiredProducts': Array<{
        productId: string;
        variantId: string;
        name: string;
        minQuantity: number;
    }>;
    'discountedProducts': Array<{
        'productId': string;
        'variantId': string;
        'maxQuantity': number;
        discountedPrice: number;
    }>;
};
/** 7. Tặng coupon
* Việc tặng coupon cho khách hàng giúp bạn giữ chân được khách hàng,
* gia tăng thị phần trong một thời gian ngắn, kích hoạt sự quan tâm của khách hàng dành cho nhãn hiệu.
*
* 8. Ưu đãi dành cho khách hàng thân thiết
 * Việc giữ chân khách hàng là một điều rất là quan trọng, nhất là đối với các khách hàng thân thiết.
 * Việc tạo ra các ưu đãi dành cho nhóm khách hàng thân thiết này sẽ giúp cho nhóm khách hàng này
 * cảm nhận được sự quan trọng của họ đối với nhãn hiệu, giữ vững sự hài lòng của khách.
*/
type CouponGiftCondition = {
    'minOrderQuantity': number;
    'minOrderValue': number;
    'couponId': string;
};
export {};
