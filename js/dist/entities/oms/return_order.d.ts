/**
 *  <PERSON><PERSON><PERSON>ng nên update return_orders trực tiếp vào orders
 * Lý do quan trọng nhất là tính đúng đắn của dữ liệu. Nếu cập nhật trả hàng trực tiếp vào orders, bạn sẽ gặp các vấn đề:
 *
 * Ph<PERSON> vỡ lịch sử đơn hàng: Một đơn hàng sau khi hoàn tất có thể bị sửa đổi khi có trả hàng, làm mất đi trạng thái ban đầu của đơn.
 * Phức tạp trong truy vấn: Mỗi đơn hàng có thể có nhiều lần trả hàng, nếu lưu vào orders, việc truy vấn các lần trả hàng sẽ phức tạp hơn.
 * Kh<PERSON> kiểm soát báo cáo: <PERSON><PERSON><PERSON> l<PERSON>ung, bạn sẽ khó tính toán tổng doanh thu thực tế hay tổng hàng đã trả, vì phải tách logic trong query.
*
* Tại sao nên tách return_orders ra thành collection riêng?
 * Dễ dàng truy vấn dữ liệu: Mỗi lần trả hàng là một record riêng, dễ dàng truy vấn và thống kê.
 * Bảo toàn lịch sử đơn hàng gốc: orders vẫn giữ nguyên trạng thái ban đầu, giúp kiểm tra dễ dàng hơn.
 * Hỗ trợ kế toán & chứng từ: Khi cần xuất hóa đơn hoàn hàng, có thể xử lý riêng trên
 */
import { Time } from '@type/utils/common';
import { Order } from '@entities/oms/order/order';
export interface ReturnOrder {
    _id: string;
    companyId: string;
    storeId: string;
    branchId: string;
    orderId: string;
    items: Order['items'];
    summary: Order['summary'];
    time?: Time;
    returnInfo?: {
        reason: string;
        code?: any;
        role?: string[];
        by?: 'guest' | 'cashier' | 'driver' | 'system';
    };
    shift: {
        _id: string;
        shiftId: number;
    };
    returnDate: Date;
}
