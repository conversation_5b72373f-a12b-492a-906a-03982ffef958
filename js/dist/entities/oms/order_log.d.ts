export interface Log {
    timestamp: Date;
    eventType: 'status_updated' | 'edited_by_staff' | 'note_added' | 'payment_updated' | 'delivery_updated' | 'cancelled' | string;
    eventCode?: 'ORDER_EDITED_BY_STAFF' | 'ORDER_NOTE_ADDED';
    details: {
        field?: string;
        oldValue?: any;
        newValue?: any;
        [key: string]: any;
    };
    performedBy?: {
        type: 'user' | 'system' | 'customer' | 'driver';
        id?: string;
        name?: string;
    };
    message?: string;
}
export interface OrderLog {
    orderId: string;
    /**
     * c<PERSON> thể ghi 83,886 phần tử.
     * tho<PERSON>i mái
     */
    logs: Log[];
}
