interface CodSettlement {
    amount: number;
    transactionId: string;
    settledAt: Date;
    note?: string;
}
interface CodReconciliation {
    expectedAmount: number;
    actualAmount: number;
    status: 'pending' | 'reconciled' | 'discrepant';
    discrepancy?: {
        amount: number;
        reason?: string;
    };
    reconciledAt?: Date;
}
export interface CodDebt {
    _id?: string;
    orderId: string;
    amount: number;
    status: 'pending' | 'partial' | 'settled' | 'failed';
    carrier?: 'ghn' | 'ghtk' | 'vietpost' | 'internal' | string;
    settlements: CodSettlement[];
    reconciliation?: CodReconciliation;
    note?: string;
    createdAt: Date;
    updatedAt: Date;
}
export {};
