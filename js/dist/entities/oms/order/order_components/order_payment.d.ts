interface Payment {
    paymentMethod: 'cash' | 'bank_transfer' | 'credit_card' | 'ewallet' | 'installment' | 'cod' | 'other';
    amount: number;
    paidAt: Date;
    transactionId?: string;
    details?: {
        bankTransfer?: {
            account: {
                accountNumber: string;
                accountHolder: string;
                bankName: string;
                branchName?: string;
            };
        };
        ewalletProvider?: 'momo' | 'zaloPay' | 'paypal' | string;
        installmentPlan?: {
            months: number;
            monthlyAmount: number;
            interestRate: number;
        };
    };
    note?: string;
}
export type OrderPayment = {
    totalAmount: number;
    payments: Payment[];
    paidAmount: number;
    remainingAmount: number;
    /**
     * Đã thanh toán
      * Đã nhập trả một phần
      * Đã thanh toán một phần
      * Chờ xử lý
      * Đã nhập trả
      * Chưa thanh toán
      * Đã hủy
     */
    paymentStatus: 'pending' | 'cancelled' | 'returned' | 'unpaid' | 'partially_paid' | 'partially_returned' | 'paid';
};
export {};
