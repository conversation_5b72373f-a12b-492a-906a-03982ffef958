import { Place } from "@type/utils/address";
interface DeliveryInfo {
    name: string;
    phoneNumber: string;
    address: Place;
    pickupAddress: Place;
    weight?: number;
    dimensions?: {
        height: number;
        length: number;
        width: number;
    };
    tryOn: "allow_view_only" | "allow_try" | "no_view" | "allow_view_no_charge";
    deliveryFeeForCustomer: number;
    returnFeeForPartner: number;
    shippingPayer: "customer" | "shop";
    deliveryMethod: "pickup_by_courier" | "drop_off_at_post";
    pickupTime: "all_day" | "8h-12h" | "13h-17h";
    returnFee: number;
    autoSendToCarrier: boolean;
    note?: string;
}
interface ShippingCarrier {
    _id?: string;
    name: 'ghn' | 'ghtk' | 'vietpost' | 'internal' | 'grabfood' | 'shopeefood' | string;
    phoneNumber?: string;
    avatar?: string;
    trackingNumber?: string;
}
interface Driver {
    name?: string;
    phoneNumber?: string;
    avatar?: string;
    estTimeToArrive?: number;
}
export interface OrderDelivery {
    deliveryType: 'physical' | 'digital' | 'service' | 'selfPickup';
    /**
     * Chờ lấy hàng: Đơn hàng sau khi đóng gói nhà vận chuyển sẽ tạo thành phiếu giao hàng ở trạng thái chờ giao hàng.
      * Đang đi lấy: Sau khi vận đơn được tạo và nhà vận chuyển xác nhận, bưu tá sẽ đến địa chỉ kho hàng của người bán để lấy hàng nhập kho nhà vận chuyển.
      * Đang giao hàng: Khi nhà vận chuyển chuyển hàng thì hệ thống sẽ chuyển sang trạng thái Đang giao hàng.
      * Đã giao hàng: Khi nhà vận chuyển giao hàng đến người mua và cập nhật trạng thái thành công.
      * Hủy giao hàng
      * Chờ chuyển hoàn: Nhà vận chuyển không phát được vì lý do khách quan, nhà vận chuyển thông báo cho người gửi và người gửi yêu cầu chuyển hoàn vận đơn cho người gửi.
      *   Một số lý do chờ chuyển hoàn:
      *     Người mua vì một lý do gì đó mà hẹn phát lại lần sau.
      *     Người mua từ chối nhận hàng khi hàng hóa không đúng như mô tả, sai màu sắc, kích thước.
      *     Thông tin người nhận không chính xác (địa chỉ, số điện thoại).
      *     Lý do khác.
      * Không gặp khách
      * Chưa hoàn thành
     */
    deliveryStatus?: 'waiting_for_pickup' | 'on_the_way_to_pickup' | 'unreachable_customer' | 'waiting_for_return' | 'in_delivery' | 'cancelled' | 'shipped' | 'delivered' | 'failed' | 'returned' | 'fulfilled';
    physicalDelivery?: {
        carrier?: ShippingCarrier;
        distance?: number;
        fee?: number;
        driver?: Driver;
        deliveryInfo?: DeliveryInfo;
    };
    digitalDelivery?: {
        downloadLink?: string;
        accessCode?: string;
        expirationDate?: Date;
    };
    serviceDelivery?: {
        appointmentTime?: Date;
        location?: string;
    };
    note?: string;
    deliveryDate?: Date;
    shippedAt?: Date;
    deliveredAt?: Date;
}
export {};
