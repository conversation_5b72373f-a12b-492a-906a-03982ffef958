import { Place } from "@type/utils/address";
interface CommunicationPlatform {
    platform: 'zalo' | 'whatsapp' | 'facebook' | 'email' | string;
    id?: string;
    name?: string;
    status?: {
        friendRequest?: {
            sent?: boolean;
            count: number;
            lastSent?: Date;
        };
        bill?: {
            sent?: boolean;
            lastSent?: Date;
        };
        delivering?: {
            sent?: boolean;
            lastSent?: Date;
        };
    };
}
export interface OrderCustomer {
    customerId?: string;
    /**
     * customer này để push vào customers
     * không có trong invoice của mongodb
     */
    customer?: {
        _id?: string;
        name: string;
        phoneNumber: string;
        address: Place;
        source?: string;
        avatar?: string;
        facebook?: string;
        note?: string;
        gender?: 'male' | 'female';
    };
    /**
     * 1 invoice có thể có nhiều customer
     * vì user có thể đổi số đt
     */
    customers?: Array<{
        _id: string;
        name: string;
        phoneNumber: string;
        hasPlatform: {
            zalo?: boolean;
        };
        communication?: CommunicationPlatform[];
    }>;
}
export {};
