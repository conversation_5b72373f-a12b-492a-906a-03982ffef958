import { OrderThirdPartyCommision, ThirdPartyName } from '../order_core';
import { OrderDiscount } from './order_discount';
import { EmbeddedProduct } from '@entities/ims/product/embedded_product';
export type OrderItem = {
    quantity: number;
    /**
     * Tổng giảm giá áp dụng cho item.
     */
    discount: number;
    /**
     * Tỷ lệ giảm giá áp dụng cho item.
     */
    discountRatio: number;
    /**
     * Tổng giá trị các mặt hàng trước thuế và giảm giá.
     */
    subTotal: number;
    /**
     * Tổng tiền khách hàng phải thanh toán (sau giảm giá, trước phí sàn).
     */
    total: number;
    /**
     * Tổng tiền thuế áp dụng cho item.
     */
    tax: number;
    /**
     * Phí nền tảng (Grab, Shopee, v.v.).
     */
    thirdPartyFee?: number;
    /**
     * Tổng doanh thu thực nhận sau khi trừ phí nền tảng.
     */
    netTotal: number;
};
export interface ThirdPartyOrderItem extends OrderItem {
    partyName: ThirdPartyName;
    productId: string | number;
    variantId: string;
    name: string;
    thirdPartyTotalDifference?: number;
    commission?: OrderThirdPartyCommision;
}
export type OrderItemProduct = EmbeddedProduct & {
    /**
     * 2 field này cho custom item
     * hoặc khi user đổi tên
     * nếu user không thay đổi, mặc định giống trong product
     */
    userOverride?: {
        name?: string;
        price?: number;
    };
};
/**
 * các món gọi thêm trong mô hình FnB
 */
export type ModifierItem = OrderItem & {
    _id: string;
    thirdPartyItem?: ThirdPartyOrderItem;
    product?: OrderItemProduct;
};
export type ModifierGroup = {
    _id: string;
    name: string;
    modifiers: Array<ModifierItem>;
};
export type OrderItemBaseDetails = {
    discount?: number;
    quantity: number;
    /**
     * topping gọi thêm
     */
    /**
     * lưu ý về số lượng trong modifierGroups
     * ví dụ 1 người gọi 2 suất xôi thập cẩm thêm 1 trứng ốp
     * thì khi tính subtotal của options phải là 2 trứng ốp
     *
     * nếu thêm gọi 2 suất xôi thập cẩm thêm 3 trứng ốp
     * thì khi tính subtotal của options phải là 6 trứng ốp
     * => subtotal = option.quantity * item.quantity * option.price
     */
    modifierGroups?: Array<ModifierGroup>;
    thirdPartyItem?: ThirdPartyOrderItem;
    product?: OrderItemProduct;
    hasPromotion?: boolean;
    discountInfo?: Array<OrderDiscount>;
    note?: string;
    internalNote?: string;
    isCustomItem?: boolean;
};
export type OrderItemDetails = OrderItemBaseDetails & OrderItem & {
    thirdPartyTotalDifference: number;
    product: OrderItemProduct;
    subTotal: number;
    total: number;
    netTotal: number;
    totalCost: number;
    /**
     * netTotal - totalCost
     */
    grossProfit: number;
};
