import { Time } from '../../../types/utils/common';
import { OrderCustomer } from './order_components/order_customer';
import { OrderDiscount } from './order_components/order_discount';
import { OrderItemDetails, OrderItem } from './order_components/order_item';
import { OrderPayment } from './order_components/order_payment';
import { OrderDelivery } from './order_components/order_delivery';
import { OrderCod } from './order_components/order_cod';
export type OrderRequest = {
    discounts?: Array<OrderDiscount>;
    customer: OrderCustomer['customer'];
    items: Array<OrderItemDetails>;
    payment: OrderPayment;
    delivery?: OrderDelivery;
    /**
     * https://support.haravan.com/support/solutions/articles/42000066981-danh-s%C3%A1ch-%C4%91%C6%A1n-h%C3%A0ng
     */
    status: OrderStatus;
    saleChannel?: {
        _id: string;
        name: 'direct' | 'phone' | 'facebook' | ThirdPartyName | 'others';
    };
    /**
     * số lượng khách
     */
    numberOfGuest?: number;
    note?: string;
    internalNote?: string;
    /**
     * Đ<PERSON> xuất đặt warehouse trong Order (như ví dụ trên) vì nó là thông tin cấp đơn hàng,
     * không chỉ liên quan đến vận chuyển.
     * Điều này cũng giúp bạn dễ dàng quản lý kho khi tích hợp với module MS hoặc WMS sau này.
     */
    warehouse: {
        _id: string;
        name: string;
    };
    tags: {
        _id: string;
        name: string;
    };
    /**
     * nhân viên tạo đơn hàng
     */
    createdBy: {
        _id: string;
        name: string;
    };
    times?: {
        createdAt?: Date;
        /**
         * preparing preorder
         */
        preparingAt?: Date;
        deliveredAt?: Date;
        completedAt?: Date;
        expiredAt?: Date;
        acceptedAt?: Date;
        cancelledAt?: Date;
        readyAt?: Date;
        displayedAt?: Date;
        driverArriveRestoAt?: Date;
        foundDriverAt?: Date;
    };
};
export type OrderBase = OrderRequest & {
    companyId: string;
    storeId: string;
    branchId: string;
    currency?: string;
    _id?: string;
    shift?: {
        _id: string;
        shiftId: number;
    };
    /**
     * để in ra bill và có vấn đề gì khách chụp bill
     * truy ngược lại orderId
     */
    orderId?: string;
    /**
     * thứ tự order trong ngày, bắt đầu từ 1
     */
    orderSequence?: number;
    /**
     * gửi  delivery status cho khách từ pos
     */
    sendDeliveryStatusToCustomer?: boolean;
    isSentDeliveryStatusToCustomer?: boolean;
    state: 'new' | 'archived' | 'cancelled' | 'non_cancelable';
    feedback?: {
        rating?: number;
        comment?: string;
        images?: string[];
    };
    thirdPartyOrder?: {
        billId: string;
        partyName: string;
        id: string | number;
        code?: string | number;
        preparationTaskID?: string;
        bookingCode?: string;
    };
    thirdPartyCommission?: OrderThirdPartyCommision;
    preparationTaskID?: string;
    customerFare?: {
        parkingFee?: number;
        surchargeFee?: number;
        packingFee?: number;
        handDeliverFee?: number;
        smallOrderFee?: number;
        serviceFee?: number;
        thirdPartyDiscount?: number;
        merchantDiscount?: number;
        itemDiscount?: number;
        shippingFee?: number;
        badWeatherFee?: number;
    };
    summary?: {
        countItems: number;
        /**
         * chưa trừ discount
         */
        subTotal: number;
        /**
         * Tổng tiền khách hàng phải thanh toán (sau giảm giá, trước phí sàn).
         */
        total: number;
        /**
         * đã trừ discount
         * và thirdPartyTotalFee
         */
        netTotal: number;
        tax: number;
        thirdPartyTotalFee: number;
        thirdPartyTotalDifference: number;
        totalDiscount: number;
        discountRatio: number;
        /**
         * netTotal - Chi phí nguyên liệu (COGS).
         */
        grossProfit: number;
        /**
         * Operating Profit (Lợi nhuận hoạt động) = Gross Profit - Chi phí cố định (thuê mặt bằng, lương nhân viên, điện nước, marketing, v.v.).
         */
        operatingProfit: number;
        /**
         * Net Profit (Lợi nhuận ròng) = Operating Profit - Chi phí khác (thuế, lãi vay, v.v.).
         */
        netProfit: number;
        quantities?: {
            [category: string]: number;
        };
        thirdPartyOrderSummary?: {
            subTotal: number;
            total: number;
            totalDiscount: number;
            discountRatio: number;
        };
    };
    cancelInfo?: {
        reason: string;
        code?: any;
        role?: string[];
        by?: 'guest' | 'cashier' | 'driver' | 'system';
    };
    scheduledOrderInfo?: {
        expectedDeliveryTime?: Date;
        pickupTime?: Date;
    };
    metaData: {
        isFromAds?: boolean;
        isGrabOrderReady?: boolean;
        [key: string]: any;
    };
    time?: Time;
    isFinalized?: boolean;
    kiotvietData?: any;
    shopeeData?: any;
    grabData?: any;
};
export interface OrderCore extends OrderBase {
    items: Array<OrderItemDetails>;
    thirdPartyOrder?: OrderBase['thirdPartyOrder'] & OrderItem;
    shift: {
        _id: string;
        shiftId: number;
    };
    /**
     * hiện ra bill
     */
    billDisplay: {
        summary: {
            subTotal: string;
            total: string;
            discount?: string;
            extraFee?: {
                name: string;
                total: string;
            };
        };
        discounts?: Array<{
            name: string;
            amount: string;
        }>;
    };
    cod?: OrderCod;
    sendNotificationStatus?: {
        newOrder?: boolean;
        cancelledOrder?: boolean;
    };
}
export type OrderThirdPartyCommision = {
    amount?: number;
    rate: number;
};
export type ThirdPartyName = 'grabfood' | 'shopeefood' | 'shopee' | 'lazada' | 'tiki' | 'sendo' | 'tiktokshop';
/**
 * Đã xác thực: Nhà bán hàng đã xác thực đơn hàng
  * Chuyển cửa hàng:	Đơn hàng đã được chuyển sang chi nhánh khác để xử lý (khi nhà bán hàng sử dụng quy trình xử lý đơn hàng nâng cao)
  * Còn hàng:	Chi nhánh nhận đơn xác nhận còn hàng (khi nhà bán hàng sử dụng quy trình xử lý đơn hàng nâng cao)
  * Hết hàng:	Chi nhánh nhận đơn xác nhận hết hàng (khi nhà bán hàng sử dụng quy trình xử lý đơn hàng nâng cao)
  * Chờ đóng gói: Thao tác xác nhận đóng gói là bước cần thiết để nhân viên phân biệt các đơn đã được chuẩn bị, tránh trường hợp hai nhân viên cùng đóng gói cho 1 đơn hàng. Thao tác này được thực hiện sau khi tạo vận đơn, nhân viên sẽ quét mã vận đơn hoặc mã đơn hàng để xác nhận Đã đóng gói.
  * Đã đóng gói:	Nhân viên đã quét mã vận đơn hoặc mã đơn hàng để xác nhận Đã đóng gói.
  * Chờ vận chuyển: Sau khi xác nhận đã đóng gói, chờ nhân viên ship đến nhận
  * Đã xuất kho:	Chi nhánh nhận đơn xác nhận xuất kho cho đơn hàng (khi nhà bán hàng sử dụng quy trình xử lý đơn hàng nâng cao)
  * Đang NVC:	Đơn đã giao qua nhà vận chuyển và nhà vận chuyển đang giao (khi nhà bán hàng sử dụng quy trình xử lý đơn hàng nâng cao)
  * Tự giao:	Khi nhà bán hàng chọn nhà vận chuyển khác (khi nhà bán hàng sử dụng quy trình xử lý đơn hàng nâng cao)
  * Hoàn tất:	Đơn hàng được bấm xác nhận hoàn tất (khi nhà bán hàng sử dụng quy trình xử lý đơn hàng nâng cao)
 * canceled_after_completion
 * liên quan đến hóa đơn chứng từ nên phải tách riêng
 *
 * Cập nhật tồn kho
 * Cập nhật công nợ & kế toán
 * xuất hóa đơn hủy
 */
export type OrderStatus = 'pending' | 'confirmed' | 'transferred_to_store' | 'in_stock' | 'out_of_stock' | 'pending_packing' | 'packed' | 'ready_to_ship' | 'warehouse_released' | 'in_transit' | 'self_delivered' | 'preparing' | 'cancelled' | 'preorder' | 'delivering' | 'completed' | 'cancelled_after_completion';
