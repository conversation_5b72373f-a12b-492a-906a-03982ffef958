interface DebtPayment {
    orderId: string;
    amount: number;
    paymentMethod: 'cash' | 'bank_transfer' | 'credit_card' | 'other';
    paidAt: Date;
    transactionId?: string;
    note?: string;
}
export interface CustomerDebt {
    _id?: string;
    customerId: string;
    totalDebt: number;
    payments: DebtPayment[];
    orders: string[];
    status: 'open' | 'closed';
    createdAt: Date;
    updatedAt: Date;
}
export {};
