/**
 * So sánh giữa cashbook và accounting_documents
 *
 * cashbook và accounting_documents đều là các bộ dữ liệu liên quan
 * đến giao dịch tài chính trong hệ thống POS, nhưng chúng có mục đích và cách sử dụng khác nhau:
 *
 * cashbook:
 * Mục đích: <PERSON> dõi chi tiết các giao dịch tiền mặt và các khoản thu/chi theo từng dòng.
 * Đ<PERSON>y là nơi bạn ghi nhận mọi giao dịch tài chính trong hoạt động hàng ngày của cửa hàng.
 * Đặc điểm: <PERSON><PERSON> gồm các khoản thu chi có thể là thanh toán từ khách hàng, tiền trả cho nhà cung cấp,
 * chi ph<PERSON> vận hành, cá<PERSON> khoản thu khác như từ đối tác, v.v.
 * Dùng khi: Cần theo dõi dòng tiền trong cửa hàng để đảm bảo tính thanh kho<PERSON>n,
 * kiểm soát chi tiêu, theo dõi tiền mặt.
 *
 *
 * accounting_documents:
 * Mục đích: Ghi nhận các chứng từ kế toán liên quan đến các giao dịch tài chính,
 * giúp phục vụ cho báo cáo kế toán, thuế, và các giao dịch kiểm soát tài chính lớn hơn.
 * Đặc điểm: Bao gồm các chứng từ có tính chất pháp lý như hóa đơn bán hàng, phiếu thu/chi,
 * chứng từ điều chỉnh công nợ, hóa đơn VAT, và các báo cáo tài chính.
 * Dùng khi: Cần xuất ra các báo cáo thuế hoặc tài liệu pháp lý (hóa đơn VAT, báo cáo tài chính, chứng từ
 * thanh toán công nợ).
 *
 * Có cần chia làm 2 collection không?
 * 1️⃣ Tại sao nên chia thành 2 collection?
 * Mặc dù cashbook và accounting_documents có một số giao điểm, nhưng bạn nên giữ chúng tách biệt vì các lý do sau:
 *
 * Mục đích sử dụng khác nhau:
 *
 * cashbook tập trung vào quản lý dòng tiền (tiền mặt, thanh toán, thu chi hàng ngày).
 *  accounting_documents là chứng từ kế toán cần thiết cho các báo cáo tài chính, quyết toán thuế, hay các đối tác kiểm toán.
 * Quản lý dễ dàng hơn:
 *
 * Việc giữ chúng riêng biệt giúp quản lý dễ dàng hơn trong việc truy xuất thông tin,
 * tránh việc lẫn lộn giữa các giao dịch thu/chi hàng ngày và các chứng từ có tính chất
 * chính thức (như hóa đơn, phiếu thu/chi có thể cần được xác nhận và xuất ra báo cáo).
 *
 * Tối ưu hiệu suất truy vấn:
 *
 * Việc phân chia sẽ giúp truy vấn các giao dịch thu/chi hàng ngày nhanh chóng trong cashbook mà không bị vướng các thông tin kế toán, giúp giảm thiểu khối lượng dữ liệu khi cần lấy báo cáo nhanh chóng.
 *
 * Tuân thủ các yêu cầu kế toán và thuế:
 *
 * Các báo cáo thuế, tài chính yêu cầu chứng từ phải có tính pháp lý, điều này làm cho việc phân chia giữa giao dịch tiền mặt thông thường và chứng từ kế toán là cần thiết để đáp ứng yêu cầu của pháp luật và quy trình kế toán.
 *
 * 2️⃣ Tuy nhiên, nếu cần có sự kết hợp...
 * Nếu hệ thống của bạn yêu cầu sự kết nối chặt chẽ giữa các giao dịch tiền mặt và chứng từ kế toán
 * (chẳng hạn khi có một khoản thu/chi phát sinh từ chứng từ kế toán), bạn có thể liên kết chúng thông qua các trường như relatedTransactionId, relatedDocumentType trong từng collection.
 *
 * Ví dụ: Khi ghi nhận một hóa đơn bán hàng trong accounting_documents, bạn có thể tạo một phiếu thu trong cashbook với cùng một relatedOrderId.
 *
 * 🔥 Kết luận
 * Nên tách chúng ra thành 2 collection vì mục đích sử dụng khác nhau và yêu cầu về quản lý, báo cáo.
 * cashbook giúp theo dõi dòng tiền hàng ngày, còn accounting_documents là các chứng từ kế toán giúp chuẩn bị cho các báo cáo thuế, tài chính.
 * Nếu cần, bạn có thể liên kết giữa các collection để dễ dàng tra cứu và kết nối dữ liệu khi cần.
 */
export interface AccountingDocument {
    _id: string;
    companyId: string;
    storeId: string;
    branchId: string;
    type: 'income' | 'expense' | 'adjustment' | 'tax';
    category: IncomeCategory | ExpenseCategory | AdjustmentCategory | VatCategory;
    referenceId: string;
    referenceType: 'order';
    amount: number;
    status: 'comfirmed';
    createdAt: Date;
}
/**
 * sales_invoice:	Ghi nhận doanh thu từ đơn hàng
 * customer_receipt: Khách hàng thanh toán công nợ hoặc đặt cọc
 * other_income_receipt: Các khoản thu ngoài bán hàng (thu từ đối tác, hỗ trợ từ hãng, thu bồi thường...)
 */
type IncomeCategory = 'sales_invoice' | 'customer_receipt' | 'other_income_receipt';
/**
 * purchase_invoice: Ghi nhận chi phí nhập hàng từ nhà cung cấp
 * supplier_payment: Thanh toán công nợ nhà cung cấp
 * salary_payment: 	Ghi nhận chi phí trả lương
 * waste_expense: xuất hủy
 * customer_refund_payment: Hoàn tiền cho khách khi trả hàng
 * operation_expense_payment: Chi phí thuê mặt bằng, điện, nước, internet...
 * marketing_expense_payment: Thanh toán chi phí quảng cáo
 * other_expense_payment: Các khoản chi khác
 */
type ExpenseCategory = 'purchase_invoice' | 'supplier_payment' | 'salary_payment' | 'customer_refund_payment' | 'operation_expense_payment' | 'marketing_expense_payment' | 'other_expense_payment' | 'waste_expense';
/**
 * customer_debt_adjustment: Điều chỉnh công nợ khách do sai sót
 * supplier_debt_adjustment: Điều chỉnh công nợ nhà cung cấp
 * cash_adjustment: Kiểm kê quỹ, xử lý chênh lệch
 * inventory_adjustment: Chênh lệch kiểm kho do hao hụt, mất mát
 */
type AdjustmentCategory = 'customer_debt_adjustment' | 'supplier_debt_adjustment' | 'cash_adjustment' | 'inventory_adjustment';
/**
 * vat_invoice: Xuất hóa đơn giá trị gia tăng (GTGT)
 * tax_declaration: 	Ghi nhận thuế phải nộp (VAT, thuế TNDN, thuế TNCN...)
 * bank_statement: 	Sao kê ngân hàng dùng để đối soát
 * financial_statement: Báo cáo tài chính, quyết toán thuế
 */
type VatCategory = 'vat_invoice' | 'tax_declaration' | 'bank_statement' | 'financial_statement';
export {};
