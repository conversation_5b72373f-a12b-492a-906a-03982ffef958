import { Time } from '@type/utils/common';
/**
 * <PERSON>ếu thu/chi đư<PERSON> tạo tự động
 *
 * Một số trường hợp sau phiếu thu chi sẽ được tạo trong quá trình vận hành cửa hàng
 * không cần phải vào màn hình Sổ quỹ để tạo. <PERSON><PERSON>c trường hợp như sau:
 *
 * Nhập hàng
 * Thanh toán cho NCC -> Phiếu chi -> Chi tiền trả NCC **
 *
 * Chi phí nhập
 * ** Thanh toán chi phí nhập khác -> Phiếu chi -> Chi tiền trả phí nhập khác **
 *
 * Tr<PERSON> hàng nhập
 * ** Thu của NCC -> Phiếu thu -> Thu tiền NCC hoàn trả **
 *
 * Chi phí trả hàng
 * ** <PERSON>h toán chi phí trả hàng -> Phiếu chi -> <PERSON> tiền trả phí trả hàng **
 *
 * <PERSON>ơn hàng POS/COD
 * ** Thu tiền của khách -> <PERSON>ếu thu -> Thu tiền khách trả **
 *
 * Hoàn tiền khách
 * ** Hoàn trả tiền cho khách -> Phiếu chi -> Chi tiền trả khách **
 *
 * Giao hàng
 * ** Nhận tiền COD của NVC -> Phiếu thu -> Thu tiền NVC **
 *
 * Chi phí vận chuyển
 * ** Trả phí giao hàng cho NVC -> Phiếu chi -> Chi tiền trả phí vận chuyển cho NVC **
 */
export interface Cashbook {
    _id: string;
    companyId: string;
    storeId: string;
    branchId: string;
    type: 'income' | 'expense' | 'adjustment';
    category: IncomeCategory | ExpenseCategory | AdjustmentCategory;
    amount: number;
    paymentMethod: 'cash' | 'bank_transfer';
    description: string;
    date: Date;
    time: Time;
    status: 'pending' | 'completed' | 'cancelled';
    /**
     * Phiếu thu (RECEIPT): Khi nhận tiền từ khách hàng, nhà cung cấp, nhân viên...
     */
    'payer'?: {
        'type': 'supplier' | 'customer' | 'employee';
        'id': string;
        'name': string;
    };
    /**
     * Phiếu chi (PAYMENT): Khi chi tiền cho nhà cung cấp, nhân viên, đối tác...
     */
    'receiver'?: {
        'type': 'supplier' | 'customer' | 'employee';
        'id': string;
        'name': string;
    };
    employee: {
        _id: string;
        name: string;
    };
    'recordAsDebt': boolean;
    'recordAsPnL': boolean;
    details?: any;
    tags: string[];
    note: string;
    /**
     * hình ảnh chứng từ thanh toán
     */
    images: Array<{
        url: string;
    }>;
    /**
     * mã tham chiếu của giao dịch
     */
    referenceCode?: string;
    referenceDocuments: Array<{
        documentType: ReferenceDocumentType;
        documentId: string;
    }>;
}
/**
 * sales_revenue: Doanh thu từ bán hàng
 * customer_debt_payment: Khách hàng thanh toán công nợ
 * return_refund_income: Khách trả hàng nhưng không hoàn tiền (trả bằng voucher)
 * partner_payment: Nhận tiền từ các sàn thương mại điện tử
 * other_income: Các khoản thu khác (không liên quan đến bán hàng)
 */
type IncomeCategory = 'sales_revenue' | 'customer_debt_payment' | 'return_refund_income' | 'partner_payment' | 'other_income';
/**
 * operation_expense: Tiền điện, nước, thuê mặt bằng, lương nhân viên
 * purchase_payment: Thanh toán tiền nhập hàng từ nhà cung cấp
 * marketing_expense: Tiền quảng cáo, khuyến mãi, Quảng cáo Facebook, Shopee Ads...
 * customer_refund: Trả tiền khách khi hủy đơn hoặc đổi trả hàng
 * supplier_debt_payment: Trả tiền nhà cung cấp khi thanh toán công nợ
 * waste_disposal: Xuất hủy, ví dụ Nếu có chi phí xử lý rác thải hoặc tiêu hủy hàng, hệ thống ghi nhận chi phí.
 * cash_withdrawal: Rút tiền  khỏi quỹ
 * other_expense: Chi phí khác
 */
type ExpenseCategory = 'purchase_payment' | 'operation_expense' | 'marketing_expense' | 'customer_refund' | 'supplier_debt_payment' | 'cash_withdrawal' | 'waste_disposal' | 'other_expense';
/**
 * cash_adjustment: Kiểm kê quỹ, điều chỉnh sai lệch sổ sách
 * fund_transfer: Chuyển tiền giữa két tiền, ngân hàng, tài khoản online
 */
type AdjustmentCategory = 'cash_adjustment' | 'fund_transfer';
type ReferenceDocumentType = 'sale_order' | 'purchase_order' | 'purchase_return' | 'sale_return' | 'customer_payment' | 'supplier_payment' | 'salary_payment' | 'adjustment' | 'expense_invoice' | 'revenue_invoice' | 'loan_payment' | 'commission_payment' | 'other';
export {};
