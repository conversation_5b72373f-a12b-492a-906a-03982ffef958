export declare const NAVIGATION_BLOCK: readonly ["cashier", "insight", "warehouse", "cashbook", "partner", "store", "marketing", "products", "supply-chain", "sales", "orders", "customers", "communication", "finance", "hr", "interactions", "business-intelligence", "settings"];
export declare const ROUTER_LINKS: {
    readonly cashier: {
        readonly order: "cashier/order";
        readonly purchases: "cashier/purchases";
        readonly reports: "cashier/reports";
        readonly invoices: "cashier/invoices";
        readonly shift_change: "cashier/shift-change";
        readonly calc_delivery_fee: "cashier/calc-delivery-fee";
        readonly reviews: "cashier/reviews";
    };
    readonly warehouse: {
        readonly inventory: "warehouse/inventory";
        readonly goods_receipt: "warehouse/goods-receipt";
        readonly goods_issue: "warehouse/goods-issue";
        readonly transfer: "warehouse/transfer";
        readonly history: "warehouse/history";
        readonly inventory_check: "warehouse/inventory-check";
        readonly draft: "warehouse/draft";
        readonly product_package: "warehouse/product-package";
        readonly inventory_limit: "warehouse/inventory-limit";
        readonly product_location: {
            readonly list: "warehouse/product-location/list";
            readonly location_list: "warehouse/location/list";
            readonly invoice: "warehouse/product-location/invoice";
            readonly product: "warehouse/product-location/product";
            readonly report: "warehouse/product-location/report";
        };
        readonly forecast: "warehouse/forecast";
        readonly internal_transport: "warehouse/internal-transport";
    };
    readonly product: {
        readonly list: "product/list";
        readonly category: "product/category";
        readonly brand: "product/brand";
        readonly attributes: "product/attributes";
        readonly batch: "product/batch";
        readonly create: "product/create";
        readonly edit: "product/edit";
        readonly layouts: "product/layouts";
        readonly layouts_edit: "product/layouts/edit";
    };
    readonly supply_chain: {
        readonly suppliers: "supply-chain/suppliers";
        readonly supplier_contracts: "supply-chain/supplier-contracts";
        readonly demand_forecast: "supply-chain/demand-forecast";
    };
    readonly sales: {
        readonly invoice_search: "sales/invoice-search";
        readonly invoice_draft: "sales/invoice-draft";
        readonly order_form: "sales/order-form";
        readonly wholesale: "sales/wholesale";
        readonly shipping: "sales/shipping";
        readonly returns: "sales/returns";
        readonly debt_gift: "sales/debt-gift";
        readonly old_device_import: "sales/old-device-import";
        readonly pricing_policy: "sales/pricing-policy";
        readonly promotions: {
            readonly discounts: "promotions/discounts";
            readonly points: "promotions/points";
            readonly coupons: "promotions/coupons";
            readonly gifts: "promotions/gifts";
        };
    };
    readonly orders: {
        readonly list: "orders/list";
        readonly duplicate: "orders/duplicate";
        readonly ecommerce: "orders/ecommerce";
        readonly packing: "orders/packing";
        readonly handover: "orders/handover";
        readonly complaints: "orders/complaints";
        readonly deleted: "orders/deleted";
        readonly reconciliation: {
            readonly cod: "orders/reconciliation/cod";
            readonly self_shipping: "orders/reconciliation/self-shipping";
        };
        readonly source: "orders/source";
    };
    readonly customers: {
        readonly potential: "customers/potential";
        readonly offer: "customers/offer";
        readonly contacts: "customers/contacts";
        readonly opportunities: "customers/opportunities";
        readonly quotations: "customers/quotations";
        readonly opportunity_pool: "customers/opportunity-pool";
        readonly campaigns: "customers/campaigns";
        readonly list: "customers/list";
        readonly card: "customers/card/deployment";
        readonly care: "customers/care";
        readonly levels: "customers/levels";
        readonly groups: "customers/groups";
        readonly care_methods: "customers/care-methods";
        readonly care_reasons: "customers/care-reasons";
    };
    readonly communication: {
        readonly conversations: "communication/conversations";
        readonly comments: "communication/comments";
        readonly reviews: "communication/reviews";
        readonly post_management: "communication/post-management";
        readonly chatbot_integration: "communication/chatbot-integration";
    };
    readonly finance: {
        readonly cash: "finance/cash";
        readonly bank: "finance/bank";
        readonly summary: "finance/summary";
        readonly debt_payment: "finance/debt-payment";
        readonly entries: {
            readonly list: "finance/entries/list";
            readonly counterpart: "finance/entries/counterpart";
            readonly installment: "finance/entries/installment";
            readonly history: "finance/entries/history";
        };
        readonly accounts: "finance/accounts";
        readonly installment_service: "finance/installment-service";
        readonly financial_reports: "finance/financial-reports";
        readonly budget_management: "finance/budget-management";
    };
    readonly hr: {
        readonly employees: "hr/employees";
        readonly attendance: "hr/attendance";
        readonly shifts: "hr/shifts";
        readonly payroll: "hr/payroll";
        readonly kpi: "hr/kpi";
        readonly recruitment: "hr/recruitment";
        readonly training: "hr/training";
    };
    readonly organization: {
        readonly departments: "organization/departments";
        readonly comments: "organization/comments";
        readonly reviews: "organization/reviews";
        readonly info: "organization/info";
    };
    readonly business_intelligence: {
        readonly overview: "business-intelligence/overview";
        readonly reports: {
            readonly overview: "reports/overview";
            readonly revenue: "reports/revenue";
            readonly orders: "reports/orders";
            readonly retail: "reports/retail";
            readonly wholesale: "reports/wholesale";
            readonly inventory: "reports/inventory";
            readonly products: "reports/products";
            readonly customers: "reports/customers";
            readonly promotions: "reports/promotions";
            readonly accounting: "reports/accounting";
        };
        readonly analytics: {
            readonly dashboard: "business-intelligence/analytics/dashboard";
            readonly revenue_forecast: "business-intelligence/analytics/revenue-forecast";
            readonly customer_behavior: "business-intelligence/analytics/customer-behavior";
        };
    };
    readonly marketing: {
        readonly commissions: "marketing/commissions";
        readonly affiliate: "marketing/affiliate";
        readonly campaign_management: "marketing/campaign-management";
        readonly roi_analysis: "marketing/roi-analysis";
        readonly advertising: "marketing/advertising";
    };
    readonly settings: {
        readonly general: "settings/general";
        readonly sales_inventory: "settings/sales-inventory";
        readonly orders: "settings/orders";
        readonly shipping: "settings/shipping";
        readonly print_template: "settings/print-template";
        readonly email: "settings/email";
        readonly sms: "settings/sms";
        readonly branches: "settings/branches";
        readonly agents: "settings/agents";
        readonly expiration: "settings/expiration";
        readonly ecommerce_sync: "settings/ecommerce-sync";
    };
};
