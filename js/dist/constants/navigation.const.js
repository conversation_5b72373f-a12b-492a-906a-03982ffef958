"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROUTER_LINKS = exports.NAVIGATION_BLOCK = void 0;
exports.NAVIGATION_BLOCK = [
    'cashier',
    'insight',
    'warehouse',
    'cashbook',
    'partner',
    'store',
    'marketing',
    'products',
    'supply-chain',
    'sales',
    'orders',
    'customers',
    'communication',
    'finance',
    'hr',
    'interactions',
    'business-intelligence',
    'settings'
];
exports.ROUTER_LINKS = {
    cashier: {
        order: 'cashier/order',
        purchases: 'cashier/purchases',
        reports: 'cashier/reports',
        invoices: 'cashier/invoices',
        shift_change: 'cashier/shift-change',
        calc_delivery_fee: 'cashier/calc-delivery-fee',
        reviews: 'cashier/reviews'
    },
    warehouse: {
        inventory: 'warehouse/inventory',
        goods_receipt: 'warehouse/goods-receipt',
        goods_issue: 'warehouse/goods-issue',
        transfer: 'warehouse/transfer',
        history: 'warehouse/history',
        inventory_check: 'warehouse/inventory-check',
        draft: 'warehouse/draft',
        product_package: 'warehouse/product-package',
        inventory_limit: 'warehouse/inventory-limit',
        product_location: {
            list: 'warehouse/product-location/list',
            location_list: 'warehouse/location/list',
            invoice: 'warehouse/product-location/invoice',
            product: 'warehouse/product-location/product',
            report: 'warehouse/product-location/report'
        },
        forecast: 'warehouse/forecast',
        internal_transport: 'warehouse/internal-transport'
    },
    product: {
        list: 'product/list',
        category: 'product/category',
        brand: 'product/brand',
        attributes: 'product/attributes',
        batch: 'product/batch',
        create: 'product/create',
        edit: 'product/edit',
        layouts: 'product/layouts',
        layouts_edit: 'product/layouts/edit'
    },
    supply_chain: {
        suppliers: 'supply-chain/suppliers',
        supplier_contracts: 'supply-chain/supplier-contracts',
        demand_forecast: 'supply-chain/demand-forecast'
    },
    sales: {
        invoice_search: 'sales/invoice-search',
        invoice_draft: 'sales/invoice-draft',
        order_form: 'sales/order-form',
        wholesale: 'sales/wholesale',
        shipping: 'sales/shipping',
        returns: 'sales/returns',
        debt_gift: 'sales/debt-gift',
        old_device_import: 'sales/old-device-import',
        pricing_policy: 'sales/pricing-policy',
        promotions: {
            discounts: 'promotions/discounts',
            points: 'promotions/points',
            coupons: 'promotions/coupons',
            gifts: 'promotions/gifts'
        }
    },
    orders: {
        list: 'orders/list',
        duplicate: 'orders/duplicate',
        ecommerce: 'orders/ecommerce',
        packing: 'orders/packing',
        handover: 'orders/handover',
        complaints: 'orders/complaints',
        deleted: 'orders/deleted',
        reconciliation: {
            cod: 'orders/reconciliation/cod',
            self_shipping: 'orders/reconciliation/self-shipping'
        },
        source: 'orders/source'
    },
    customers: {
        potential: 'customers/potential',
        offer: 'customers/offer',
        contacts: 'customers/contacts',
        opportunities: 'customers/opportunities',
        quotations: 'customers/quotations',
        opportunity_pool: 'customers/opportunity-pool',
        campaigns: 'customers/campaigns',
        list: 'customers/list',
        card: 'customers/card/deployment',
        care: 'customers/care',
        levels: 'customers/levels',
        groups: 'customers/groups',
        care_methods: 'customers/care-methods',
        care_reasons: 'customers/care-reasons'
    },
    communication: {
        conversations: 'communication/conversations',
        comments: 'communication/comments',
        reviews: 'communication/reviews',
        post_management: 'communication/post-management',
        chatbot_integration: 'communication/chatbot-integration'
    },
    finance: {
        cash: 'finance/cash',
        bank: 'finance/bank',
        summary: 'finance/summary',
        debt_payment: 'finance/debt-payment',
        entries: {
            list: 'finance/entries/list',
            counterpart: 'finance/entries/counterpart',
            installment: 'finance/entries/installment',
            history: 'finance/entries/history'
        },
        accounts: 'finance/accounts',
        installment_service: 'finance/installment-service',
        financial_reports: 'finance/financial-reports',
        budget_management: 'finance/budget-management'
    },
    hr: {
        employees: 'hr/employees',
        attendance: 'hr/attendance',
        shifts: 'hr/shifts',
        payroll: 'hr/payroll',
        kpi: 'hr/kpi',
        recruitment: 'hr/recruitment',
        training: 'hr/training'
    },
    organization: {
        departments: 'organization/departments',
        comments: 'organization/comments',
        reviews: 'organization/reviews',
        info: 'organization/info'
    },
    business_intelligence: {
        overview: 'business-intelligence/overview',
        reports: {
            overview: 'reports/overview',
            revenue: 'reports/revenue',
            orders: 'reports/orders',
            retail: 'reports/retail',
            wholesale: 'reports/wholesale',
            inventory: 'reports/inventory',
            products: 'reports/products',
            customers: 'reports/customers',
            promotions: 'reports/promotions',
            accounting: 'reports/accounting'
        },
        analytics: {
            dashboard: 'business-intelligence/analytics/dashboard',
            revenue_forecast: 'business-intelligence/analytics/revenue-forecast',
            customer_behavior: 'business-intelligence/analytics/customer-behavior'
        }
    },
    marketing: {
        commissions: 'marketing/commissions',
        affiliate: 'marketing/affiliate',
        campaign_management: 'marketing/campaign-management',
        roi_analysis: 'marketing/roi-analysis',
        advertising: 'marketing/advertising'
    },
    settings: {
        general: 'settings/general',
        sales_inventory: 'settings/sales-inventory',
        orders: 'settings/orders',
        shipping: 'settings/shipping',
        print_template: 'settings/print-template',
        email: 'settings/email',
        sms: 'settings/sms',
        branches: 'settings/branches',
        agents: 'settings/agents',
        expiration: 'settings/expiration',
        ecommerce_sync: 'settings/ecommerce-sync'
    }
};
//# sourceMappingURL=navigation.const.js.map