import { EmployeeDataInRequest } from './employee/employee';
import { StoreConfigs } from './store';
export type PosUpdateData = {
    appVersion?: string;
    countPendingOrders: number;
    countUnreadReviews: number;
    currentShift: {
        shiftId?: number;
        employees?: Array<{
            _id: string;
            name: string;
        }>;
        startAt?: Date;
        endAt?: Date;
        startAtText?: string;
    };
};
export type PosInitUserData = EmployeeDataInRequest & {
    token: string;
};
export type PosInitData = PosUpdateData & {
    user?: PosInitUserData;
    receiptionists: Array<{
        _id: string;
        name: string;
    }>;
    store: StoreConfigs;
};
