import { Time } from './common';
export type ThirdPartyPayment = {
    _id?: string;
    storeId: string;
    branchId: string;
    thirdParty: ThirdPartyName;
    store: {
        name: string;
        storeId: string;
        merchantId?: string;
        restaurantId?: string;
    };
    status: 'pending' | 'completed' | 'reverted';
    paymentType: 'auto' | 'manual';
    amount: number;
    expectedAmount?: number;
    /**
     * phải equal mới cho set paid trong finalizeDailyCashflow
     */
    isEqualAmount?: boolean;
    /**
     * distributed vao finalizeDailyCashflow
     */
    isDistributed?: boolean;
    distributedTo?: Array<{
        _id: string;
        date: Date;
        amount: number;
    }>;
    bank?: {
        name: string;
        accountName?: string;
        accountNumber: string;
    };
    transactionId?: string;
    /**
     * có thể đảo ngược payment (manual)
     */
    reversible: boolean;
    createdAt: Date;
    updatedAt?: Date;
    completedAt?: Date;
    failedAt?: Date;
    /**
     * date này để tính debt
     * với shopee là createdAt, grab là completedAt
     */
    date: Date;
    time: Time;
    /**
     * gui thong bao
     */
    announced?: boolean;
    note?: string;
    /**
     * nhiều payment từ grab hoặc shopee bị sai
     * phải chỉnh lại bằng tay để distribute payment
     */
    isManualChange?: boolean;
    shopeeData?: any;
    grabData?: any;
};
export type ThirdPartyName = 'kiotviet' | 'grab' | 'shopee';
