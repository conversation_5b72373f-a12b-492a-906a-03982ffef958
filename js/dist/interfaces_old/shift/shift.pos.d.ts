export type PosShiftEndForm = {
    _id: string;
    shiftId: number;
    cash: {
        begin?: number;
        end?: number;
        cashout?: number;
        beginErrorRange?: number;
        endErrorRange?: number;
    };
    rice: number;
    note?: string;
    date?: Date;
    endAt?: Date;
    inventory: Array<{
        _id: string;
        name: string;
        quantity: number;
        measureUnit: string;
    }>;
    ingredients: Array<{
        _id: string;
        name: string;
    }>;
};
export type PosShiftEndData = {
    shiftId: number;
    data: {
        lastShiftCash: number;
        revenueCash: number;
        purchasedCash: number;
        otherExpensesCash: number;
    };
    inventoryCheckItems: Array<{
        _id: string;
        name: string;
        measureUnit: string;
        inventoryNote: string;
        lastQuantity: number;
    }>;
    ingredientItems: Array<{
        _id: string;
        name: string;
        namevn?: string;
    }>;
    form: PosShiftEndForm;
};
