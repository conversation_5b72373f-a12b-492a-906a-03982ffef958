import { BaseMessage } from 'firebase-admin/lib/messaging/messaging-api';
/**
 * https://stackoverflow.com/questions/68295113/how-to-implement-the-publish-subscribe-pattern-in-typescript
 */
/**
 * Defines the function type of the publish function.
 *
 * Extracts the keys from `E` as valid event types, and the matching
 * property as the payload.
 */
export type QueueTypeFn<E> = <Key extends string & keyof E>(queue: Key, message: E[Key]) => void;
/**
* Defines the function type for the subscribe function.
*
* Extracts the keys from `E` as valid event types, and the matching
* property as the payload to the callback function.
*/
export type SubQueueFn<E> = <Key extends string & keyof E>(queue: Key, fn: (message: E[Key]) => Promise<any>) => void;
/**
* Defines the function type for the subscription callback. Ensures
* the message payload is a valid property of the event being used.
*/
/**
* Tie everything together.
*/
export type QueueType<E> = {
    add: QueueTypeFn<E>;
    process: SubQueueFn<E>;
};
/**
* Creates a new PubSub instance, the `E` type parameter should be a
* type enumerating all the available events and their payloads.
*
* @example
* type Events = {
*  warn: { message: string },
*  error: { message: string }
* }
*
* const pubSub = PubSub<Events>()
* pubSub.publish('warn', { message: "Something bad happened!" })
*/
export type QueueEvents = {
    newNotification: BaseMessage;
    summarizeDailyReport: BaseMessage;
};
