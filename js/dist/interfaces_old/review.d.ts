import { ThirdPartyName } from './third_party';
export type Review = {
    storeId: string;
    branchId: string;
    thirdPartyInvoice?: {
        partyName: ThirdPartyName;
        id?: string;
        code?: string;
        bookingCode?: string;
    };
    rating: number;
    comments: string[];
    reasons?: string[];
    photos?: Array<{
        thumbnail: string;
        url: string;
    }>;
    reply?: {
        content: string;
    };
    createdAt: Date;
    feedbackId?: string;
    isDeleted?: boolean;
    isRead?: boolean;
    customer: {
        name: string;
        phoneNumber?: string;
        avatar?: string;
    };
    customerId?: string;
    invoice?: {
        _id?: string;
        invoiceId?: string;
        items: string[];
        date: Date;
    };
    shift?: {
        shiftId: number;
        employees: Array<{
            _id: string;
            name: string;
        }>;
    };
};
export type ReviewSummary = {
    avg: number;
    countTotal: number;
    ratings: Array<{
        star: number;
        count: number;
    }>;
};
