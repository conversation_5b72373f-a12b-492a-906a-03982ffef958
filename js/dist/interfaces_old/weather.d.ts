export type Weather = {
    list: any;
    rainInDates?: Date[];
    nearestRainDate?: {
        date: Date;
        desc: string;
    };
};
export type Weather2 = {
    list: Array<{
        date: Date;
        maxTemp: number;
        minTemp: number;
        desc: string;
        img: string;
    }>;
    today: {
        current: {
            temp?: number;
            desc?: string;
            img?: string;
        };
        allday: {
            temp?: number;
            img?: string;
        };
        tonight: {
            temp?: number;
            img?: string;
        };
    };
    rainInDates?: Date[];
    nearestRainDate?: {
        date: Date;
        desc: string;
    };
};
