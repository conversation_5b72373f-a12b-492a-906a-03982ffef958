export type AdsMsgCampaign = {
    _id?: string;
    storeId: string;
    branchId: string;
    msgType: 'sms' | 'zalo';
    name: string;
    content: string;
    msgImages: Array<{
        _id: string;
        url: string;
    }>;
    filterSms: {
        hasZalo: boolean;
    };
    filterZalo: {
        hasZaloFriend: boolean;
    };
    filter: {
        customerBadges: string[];
        customerSources: string[];
    };
    isStarted: boolean;
    isPaused: boolean;
    isInProgress: boolean;
    isCanceled: boolean;
};
