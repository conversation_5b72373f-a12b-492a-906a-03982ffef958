import { StoreAccount } from '../store_account/account';
export type AutoIncreaseBudgetAd = {
    campaignID: string;
    set: boolean;
    lastSet: Date;
    lastUnset: Date;
};
export type AutoIncreaseBudget = {
    day: number;
    increaseToBudget: number;
    normalBudget: number;
};
export type AdPlatform = StoreAccount['platform'];
/**
 * tự động tăng ngân sách vào các ngày cố định
 */
export type AutoIncreaseBudgets = Array<{
    campaignID: string;
    day: number;
    increaseToBudget: number;
    normalBudget: number;
}>;
export type AdOverviewMetrics = {
    spend: number;
    tax: number;
    totalSpend: number;
    reach: number;
    clicks: number;
    engagement: number;
    impressions: number;
    conversions: number;
    conversionValue: number;
    linkClicks: number;
    roas: number;
};
