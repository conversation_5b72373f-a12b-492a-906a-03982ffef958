import { Time } from '../common';
import { AdPlatform } from './ads_display';
export type AdsReport = {
    storeId: string;
    branchId: string;
    platform: AdPlatform;
    campaignID: string;
    adGroupID: string;
    time: Time;
    dateStr: string;
    date: Date;
    spend: number;
    tax: number;
    taxPct: number;
    totalSpend: number;
    clicks: number;
    impressions: number;
    /**
     * chiến dịch message inline_post_engagement
     * là khi user click vào rồi ra 1 conversation mới
     */
    engagement?: number;
    reach?: number;
    linkClicks?: number;
    linkClickCtr?: number;
    /**
     * Return on spend
     * How much you’ve earned compared to the total marketing spend.
     */
    roas?: number;
    /**
     * Click-through rate. Rate of clicks on ad impression
     */
    ctr: number;
    /**
     * Total number of ad-generated orders per 1,000 impressions
     * cnv/1000
     */
    cvr?: number;
    /**
     * Cost per click. Spend for every time an ad was clicked
     */
    cpc: number;
    /**
     * CPP viết tắt là cost per rating point (<PERSON> phí quảng cáo trả cho một rating).
     * Đơn vị này rất quan trọng, nó cho bạn biết về benchmark về chi phí quảng cáo của
     * một ngành hàng hay của một đối tượng khán giả mục tiêu.
     *
     * Là cơ sở để các công ty quảng cáo làm việc về ngân sách quảng cáo với các
     * advertisers. Các media buyer làm căn cứ để deal với media owner. Để Media planner
     * lựa chọn các Phương tiện truyền thông phù hợp, và ước tính ngân sách quảng cáo.
     */
    cpp?: number;
    /**
     * Cost per 1,000 impressions
     */
    /**
     * effective cost per mille
     *
     * The main difference between CPM and eCPM is that CPM is only used in the context of
     * calculating cost per a thousand impressions in a CPM ad buying model,
     * while eCPM is a revenue metric that can be applied to any pricing method.
     * eCPM = (Total ad revenue / total impressions) x 1000
     */
    cpm: number;
    conversionValue: number;
    conversions: number;
    conversionRateRanking?: string;
    costPerUniqueClick?: number;
    costPer15SecVideoView?: number;
    costPer2SecContinuousVideoView?: number;
    costPerAdClick?: number;
    costPerConversion?: number;
    costPerLinkClick?: number;
    /**
     * chiến dịch message cost per message
     * là cái này
     */
    costPerEngagement?: number;
    /**
     * Percentage of ad-generated orders from menu visits.
     */
    checkoutRate?: number;
    /**
     * Ad spend per order attributed to the ad.
     * SUM(Ad Spend)/ Sum(Ad Generated Order)
     */
    costPerOrder?: number;
    addToCart?: number;
    menuVisit?: number;
    clickAttributedOrder?: number;
    clickAttributedProductSale?: number;
    clickAttributedProduct?: number;
    clickAttributedSale?: number;
    clickAttributedAddToCart?: number;
    clickAttributedMenuVisit?: number;
    uniqueUserAddToCart?: number;
    uniqueUserClick?: number;
    uniqueUserConversion?: number;
    uniqueUserImpression?: number;
    uniqueUserMenuVisit?: number;
    viewAttributedOrder?: number;
    viewAttributedProductSale?: number;
    viewAttributedProduct?: number;
    viewAttributedSale?: number;
    viewAttributedAddToCart?: number;
    viewAttributedMenuVisit?: number;
    grabData?: any;
    fbData?: any;
};
