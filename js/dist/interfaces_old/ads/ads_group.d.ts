import { AdPlatform } from './ads_display';
/**
 * 1 chiến dịch có nhiều ad groups
 * giới hạn ngân sách chiến dịch
 * rồi nếu có nhiều ad group thì
 * giới hạn ngân sách ad group
 */
export type AdGroupBase = {
    platform: AdPlatform;
    advertiserID: string;
    campaignID: string;
    adGroupID: string;
    adTypeID?: string;
    priorityID?: string;
    name: string;
    isActive: boolean;
    status: {
        code?: number;
        isPaused: boolean;
        status: 'active' | 'completed' | 'draft';
    };
    effectiveStatus?: string;
    configuredStatus?: string;
    startTime: Date;
    endTime: Date;
    createdAt: Date;
    updatedAt: Date;
    bidPrice?: number;
    spendingLimit?: number;
    priceType?: 'CPM' | 'CPC' | 'CPA';
    isAlwaysOn?: boolean;
    frequencyCap?: {
        type: string;
        timeUnit: string;
        duration: number;
        amount: number;
    };
    /**
     * đ<PERSON> lấy recommend data
     */
    targeting?: {
        dayParts: Array<{
            isMonday: boolean;
            isTuesday: boolean;
            isWednesday: boolean;
            isThursday: boolean;
            isFriday: boolean;
            isSaturday: boolean;
            isSunday: boolean;
            startTime: string;
            endTime: string;
        }>;
        mex: {
            chainID: string;
            mexIDs: string[];
            categoryShortcutID: string;
            deeplink: string;
        };
        keyword: {
            isAutoKeyword: boolean;
            manualKeywords: string[];
            keywordMatchType: string;
        };
        country: {
            countryCodes: string[];
        };
        city: {
            cityCodes: string[];
        };
        audience: {
            existingAudience: {
                include: any;
                exclude: any;
            };
            grabAudience: {
                include: any;
                exclude: any;
            };
            simplifiedAudienceTargeting: {
                selectedSimplifiedAudienceType: string;
                selectedYourAudienceTypes: any;
            };
        };
        placement: {
            isAutoPlacement: boolean;
            placements: Array<{
                placementD: number;
                category: string;
                name: string;
                sequenceD: number;
                placementMetadata: any;
            }>;
        };
        geoDistance: {
            geoTargets: Array<{
                latitude: number;
                longitude: number;
                radius: number;
                geoD: number;
                mexD: string;
                poiInfo: {
                    poiD: string;
                    name: string;
                    address: string;
                    city: string;
                    postcode: string;
                    location: any;
                };
            }>;
        };
    };
    offerID?: string;
    customIDs?: {
        sfLineItemID?: string;
    };
    grabData?: {
        search?: any;
        details?: any;
        summary?: any;
    };
    fbData?: any;
    logs?: string[];
};
export type AdGroup = AdGroupBase & {
    metrics: {
        impressions: number;
        clicks: number;
        conversions: number;
        spend: number;
        ctr: number;
        conversionValue: number;
        conversionsS2S?: number;
        deliveryPercentage?: number;
        /**
         * details
         */
        roas?: number;
        cvr?: number;
        clickAttributedOrder?: number;
        clickAttributedProductSale?: number;
        clickAttributedSale?: number;
        viewAttributedOrder?: number;
        viewAttributedProductSale?: number;
        viewAttributedSale?: number;
    };
};
export type AdGroupRecommends = {
    daily: {
        impressions: {
            lowerBound: number;
            upperBound: number;
        };
        clicks: {
            lowerBound: number;
            upperBound: number;
        };
        conversions: {
            lowerBound: number;
            upperBound: number;
        };
        reach: {
            lowerBound: number;
            upperBound: number;
        };
        budget: number;
    };
    total: {
        impressions: {
            lowerBound: number;
            upperBound: number;
        };
        clicks: {
            lowerBound: number;
            upperBound: number;
        };
        conversions: {
            lowerBound: number;
            upperBound: number;
        };
        reach: {
            lowerBound: number;
            upperBound: number;
        };
        budget: number;
    };
    recommendations: {
        bidPrice: {
            recommendedMin: number;
            recommended: number;
            recommendedMax: number;
            floor: number;
            cap: number;
        };
    };
};
