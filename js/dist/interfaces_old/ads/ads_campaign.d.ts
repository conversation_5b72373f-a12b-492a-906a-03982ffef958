import { AdPlatform } from './ads_display';
import { AdGroup } from './ads_group';
export type AdsCampaignBase = {
    storeId: string;
    branchId: string;
    platform: AdPlatform;
    name: string;
    campaignID: string;
    advertiserID: string;
    isActive: boolean;
    budget: {
        daily: number;
        lifetime: number;
        remain?: number;
    };
    objective: string;
    status: {
        code?: number;
        isPaused?: boolean;
        status: 'active' | 'completed' | 'draft' | string;
    };
    effectiveStatus?: string;
    configuredStatus?: string;
    buyingType?: string;
    budgetType?: string;
    pacingType?: string;
    startTime: Date;
    endTime: Date;
    createdAt?: Date;
    updatedAt?: Date;
    /**
     * de set PUT data
     */
    customIDs?: {
        sfLineItemID: string;
        GMSCampaignID: string;
        grabXExperimentVariable: string;
    };
    discountPercentage?: number;
    grabData?: {
        search?: any;
        details?: any;
    };
    fbData?: any;
};
export type AdsCampaign = AdsCampaignBase & {
    _id?: string;
    adGroups?: AdGroup[];
    metrics?: {
        spend: number;
        clicks: number;
        impressions: number;
        ctr: number;
        conversions: number;
        conversionValue: number;
        conversionsS2S?: number;
        pacingRatio?: number;
    };
    isAlwaysOn?: boolean;
    logs?: string[];
    /**
     * đây là các chức năng từ app mình
     */
    configs?: {
        /**
         * tự động tăng ngân sách vào các ngày cố định
         */
        autoIncreaseDayBudget?: {
            isActive?: boolean;
            day: number;
            increaseToBudget: number;
            normalBudget: number;
        };
        /**
         * tự động bât/ tắt ads
         */
        scheduleOnOff?: {
            isActive?: boolean;
            by: 'work_session' | 'timeframe';
            dayParts?: Array<{
                day: number;
                startTime: {
                    hours: number;
                    mins: number;
                };
                endTime: {
                    hours: number;
                    mins: number;
                };
            }>;
        };
    };
    states?: {
        autoIncreaseDayBudget?: {
            set: boolean;
            lastSet?: Date;
            lastUnset?: Date;
            nextActionAt?: Date;
        };
        scheduleOnOff?: {
            status: 'on' | 'off';
            lastSet?: Date;
            nextActionAt?: Date;
        };
    };
};
