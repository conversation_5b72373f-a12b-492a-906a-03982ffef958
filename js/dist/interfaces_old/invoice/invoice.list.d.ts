import { Invoice } from './invoice';
export type InvoiceListItem = {
    _id: string;
    invoiceId: string;
    statusText: string;
    status: Invoice['status'];
    countItem: number;
    grossProfit: number;
    grossProfitPct: number;
    total: string;
    internalNote?: string;
    note?: string;
    itemNote?: string;
    delivery: {
        selfArrive: boolean;
    };
    customer?: {
        name: string;
        address: string;
        zaloName: string;
    };
    /**
     * để sort lại
     */
    createdTime: number;
    scheduledExpectedDeliveryTime?: number;
    order: number;
    orderText: string;
    paymentMethod: Invoice['paymentMethod'];
    diningOption: Invoice['diningOption'];
    thirdPartyInvoice?: Invoice['thirdPartyInvoice'];
    rating: number;
    isFromAds: boolean;
};
export type InvoiceList = Array<InvoiceListItem>;
export type PosListInvoices = {
    history: InvoiceList;
    preparing: InvoiceList;
    customer?: InvoiceList;
};
