import { Place } from '@type/utils/address';
import { Time } from '../common';
import { ThirdPartyName } from '../third_party';
import { PosInvoice, PosInvoiceItem } from './invoice.pos';
export type InvoiceItemBase = {
    discount: number;
    discountRatio: number;
    /**
     * chưa trừ discount
     */
    subTotal: number;
    /**
     * đã trừ discount
     * và 3rdpartyCommission
     */
    total: number;
    tax?: number;
};
export type InvoiceItem = InvoiceItemBase & {
    price: number;
    quantity: number;
};
export type InvoiceBaseDetails = {
    discount?: number;
    price?: number;
    quantity: number;
    /**
     * topping gọi thêm
     */
    /**
     * lưu ý về số lượng trong options
     * ví dụ 1 người gọi 2 suất xôi thập cẩm thêm 1 trứng ốp
     * thì khi tính subtotal của options phải là 2 trứng ốp
     *
     * nếu thêm gọi 2 suất xôi thập cẩm thêm 3 trứng ốp
     * thì khi tính subtotal của options phải là 6 trứng ốp
     * => subtotal = option.quantity * item.quantity * option.price
     */
    options?: Array<InvoiceBaseDetails>;
    thirdPartyItem?: InvoiceThirdPartyItem;
    hasPromotion?: boolean;
    productId?: string;
    product?: {
        _id?: string;
        cost: number;
        name: string;
        price: number;
        originId?: string;
        category?: string;
        isMeal?: boolean;
        isDrink?: boolean;
        isCanned?: boolean;
    };
    discountInfo?: Array<InvoiceDiscount>;
    note?: string;
    isCustomItem?: boolean;
    /**
     * tinh cost theo nguyen lieu
     * voi custom item
     */
    ingredients?: PosInvoiceItem['ingredients'];
};
export type InvoiceDetails = InvoiceBaseDetails & InvoiceItem & {
    /**
     * lưu ý về số lượng trong options
     * ví dụ 1 người gọi 2 suất xôi thập cẩm thêm 1 trứng ốp
     * thì khi tính subtotal của options phải là 2 trứng ốp
     *
     * nếu thêm gọi 2 suất xôi thập cẩm thêm 3 trứng ốp
     * thì khi tính subtotal của options phải là 6 trứng ốp
     * => subtotal = option.quantity * item.quantity * option.price
     */
    options?: Array<InvoiceDetails>;
    thirdPartyTotalDifference: number;
    thirdPartyFee: number;
    product: InvoiceBaseDetails['product'];
    subTotal: number;
    total: number;
    totalCost: number;
    grossProfit: number;
};
export type InvoiceBase = {
    storeId: string;
    branchId: string;
    _id?: string;
    shiftId?: number;
    discounts?: Array<InvoiceDiscount>;
    thirdPartyCommission?: InvoiceThirdPartyCommision;
    /**
     * để in ra bill và có vấn đề gì khách chụp bill
     * truy ngược lại orderId
     */
    invoiceId?: string;
    order?: number;
    customerId?: string;
    /**
     * customer này để push vào customers
     * không có trong invoice của mongodb
     */
    customer?: InvoiceCustomer;
    /**
     * 1 invoice có thể có nhiều customer
     * vì user có thể đổi số đt
     */
    customers?: Array<{
        _id: string;
        name: string;
        phoneNumber: string;
        hasZalo?: boolean;
        zalo?: {
            name: string;
            zaloGlobalId: string;
        };
        isSentZaloFriendRequest?: boolean;
        isSentInvoice?: boolean;
        lastTrySentInvoice?: Date;
        isSentDeliveryStatus?: boolean;
        lastTrySentDeliveryStatus?: Date;
        countZaloRequests?: number;
    }>;
    items: Array<InvoiceBaseDetails>;
    /**
     * gửi  delivery status cho khách từ pos
     */
    sendDeliveryStatusToCustomer?: boolean;
    isSentDeliveryStatusToCustomer?: boolean;
    kiotvietData?: any;
    shopeeData?: any;
    grabData?: any;
    tableAndRoomName?: string;
    diningOption: 'eatin' | 'takeaway';
    status: 'completed' | 'preparing' | 'cancelled' | 'preorder' | 'delivering';
    saleChannel?: 'Direct' | 'Phone' | 'Facebook' | 'GrabFood' | 'ShopeeFood' | 'Others';
    paymentMethod?: 'bank_transfer' | 'cash' | 'grab' | 'shopee';
    /**
     * số lượng khách
     */
    numberOfGuest?: number;
    note?: string;
    internalNote?: string;
    delivery?: {
        /**
         * khách tự đến lấy
         */
        selfArrive: boolean;
        distance?: number;
        fee?: number;
        note?: string;
        company?: {
            id?: string;
            avatar?: string;
            name?: string;
            phoneNumber?: string;
        };
        driver?: {
            name?: string;
            phoneNumber?: string;
            avatar?: string;
            estTimeToArrive?: number;
        };
        customer: {
            name: string;
            phoneNumber: string;
            address: Place;
            note?: string;
        };
    };
    feedback?: PosInvoice['feedback'];
    thirdPartyInvoice?: {
        billId: string;
        partyName: ThirdPartyName;
        id: string | number;
        code?: string | number;
        preparationTaskID?: string;
        bookingCode?: string;
    };
    preparationTaskID?: string;
    times?: {
        createdAt?: Date;
        /**
         * preparing preorder
         */
        preparingAt?: Date;
        deliveredAt?: Date;
        completedAt?: Date;
        expiredAt?: Date;
        acceptedAt?: Date;
        cancelledAt?: Date;
        readyAt?: Date;
        displayedAt?: Date;
        driverArriveRestoAt?: Date;
        foundDriverAt?: Date;
    };
    customerFare?: {
        parkingFee?: number;
        surchargeFee?: number;
        packingFee?: number;
        handDeliverFee?: number;
        smallOrderFee?: number;
        serviceFee?: number;
        thirdPartyDiscount?: number;
        merchantDiscount?: number;
        itemDiscount?: number;
        shippingFee?: number;
        badWeatherFee?: number;
    };
    summarize?: {
        countItems: number;
        /**
         * chưa trừ discount
         */
        subTotal: number;
        /**
         * đã trừ discount
         * và 3rdpartyCommission
         */
        totalRevenue: number;
        tax?: number;
        thirdPartyTotalFee: number;
        thirdPartyTotalDifference: number;
        totalDiscount: number;
        discountRatio: number;
        totalQuantity: {
            meals: number;
            drinks: number;
            canned: number;
            others: number;
        };
        totalCost: number;
        drinkCost: number;
        savedDisposableItemsCosts: number;
        disposableItemsCosts: number;
        grossProfit: number;
        thirdPartySummarize?: {
            subTotal: number;
            total: number;
            totalDiscount: number;
            discountRatio: number;
        };
    };
    cancelInfo?: {
        reason: string;
        code?: any;
        role?: string[];
        by?: 'guest' | 'cashier' | 'driver' | 'system';
    };
    scheduledOrderInfo?: {
        expectedDeliveryTime?: Date;
        pickupTime?: Date;
    };
    time?: Time;
    isFinalized?: boolean;
    logs?: string[];
    isFromAds?: boolean;
    isGrabInvoiceReady?: boolean;
};
export type Invoice = InvoiceBase & {
    items: Array<InvoiceDetails>;
    thirdPartyInvoice?: InvoiceBase['thirdPartyInvoice'] & InvoiceItemBase;
    shiftId: number;
    /**
     * hiện ra bill
     */
    billDisplay: {
        summarize: {
            subTotal: string;
            total: string;
            discount?: string;
            extraFee?: {
                name: string;
                total: string;
            };
        };
        discounts?: Array<{
            name: string;
            amount: string;
        }>;
    };
    sendNotificationStatus?: {
        newOrder?: boolean;
        cancelledOrder?: boolean;
    };
    logs?: string[];
};
export type InvoiceDiscount = {
    type?: string;
    name?: string;
    amount: number;
    funding?: number;
    excludeFromSumTotalDiscount?: boolean;
};
export type InvoiceThirdPartyItem = {
    partyName: ThirdPartyName;
    productId: string | number;
    price: number;
    name: string;
    discount?: number;
    subTotal?: number;
    total?: number;
    thirdPartyTotalDifference?: number;
    thirdPartyFee?: number;
    commission?: InvoiceThirdPartyCommision;
};
export type InvoiceThirdPartyCommision = {
    amount?: number;
    rate: number;
};
export type InvoiceCustomer = {
    _id?: string;
    name: string;
    phoneNumber: string;
    address: Place;
    source?: ThirdPartyName | 'pos';
    avatar?: string;
    facebook?: string;
    note?: string;
    gender?: 'male' | 'female';
};
