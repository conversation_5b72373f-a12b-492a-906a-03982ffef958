import { Place } from '@type/utils/address';
import { Invoice, InvoiceThirdPartyItem } from './invoice';
export type PosInvoice = {
    _id?: string;
    invoiceId?: string;
    order?: number;
    discountWholeOrder: {
        amount: number;
        name?: string;
    };
    diningOption?: 'eatin' | 'takeaway';
    paymentMethod?: 'bank_transfer' | 'cash' | 'grab' | 'shopee' | 'unspecified';
    discounts?: Invoice['discounts'];
    customer: {
        _id?: string;
        name?: string;
        phoneNumber?: string;
        address?: Place;
        gender?: string;
        avatar?: string;
        facebook?: string;
        note?: string;
        allAddress?: Place[];
        latestUsedAddress?: Place;
        countOrders?: number;
        totalSold?: number;
        lastOrderAtText?: string;
        lastRatingText?: string;
        zaloName?: string;
        rank?: number;
        badges?: Array<{
            type: 'success' | 'danger' | 'primary';
            text: string;
        }>;
    };
    summarize: {
        countItems: number;
        totalDiscount: number;
        subTotal: number;
        totalRevenue: number;
        thirdPartyTotalFee?: number;
        grossProfit?: number;
        grossProfitPct?: number;
    };
    billDisplay?: Invoice['billDisplay'] & {
        posTotalAmount: number;
    };
    isFinalized: boolean;
    isThirdPartyInvoice: boolean;
    sendDeliveryStatusToCustomer?: boolean;
    feedback?: {
        rating: number;
        comments: string[];
        reasons?: string[];
        photos?: Array<{
            thumbnail: string;
            url: string;
        }>;
        reply?: {
            content: string;
        };
        createdAt: Date;
        feedbackId?: string;
        isDeleted?: boolean;
    };
    delivery: {
        /**
         * khách tự đến lấy
         */
        selfArrive: boolean;
        distance?: number;
        fee?: number;
        note?: string;
        displayAddress?: string;
        company: {
            id?: string;
            avatar?: string;
            name?: string;
            phoneNumber?: string;
        };
        driver: {
            name?: string;
            phoneNumber?: string;
            avatar?: string;
            estTimeToArrive?: number;
        };
    };
    /**
     * phần invoice summarize ko để theo obj nữa mà thành items
     * vì đến bước này cũng chả thay đổi j nữa,
     * vì có những _ids bị trùng mà đơn grab, shopee nó lại cho phép trùng
     * mà POS của mình chưa cho phép trùng id nhưng thành nhiều item
     */
    items: Array<PosInvoiceItem>;
    status?: Invoice['status'];
    thirdPartyInvoice?: Invoice['thirdPartyInvoice'];
    times?: {
        createdAt?: Date;
        completedAt?: Date;
        cancelledAt?: Date;
        deliveredAt?: Date;
    };
    scheduledOrderInfo?: Invoice['scheduledOrderInfo'];
    statusText?: string;
    timeline: Array<{
        isActive?: boolean;
        class?: string;
        text: string;
    }>;
    note?: string;
    internalNote?: string;
    shiftId?: number;
    payment?: {
        qrPaymentUrl?: string;
        amount: number;
    };
    /**
     * public bill không có field này
     * do user ko authenticate
     * nên fai manual add vào
     */
    store?: {
        storeId: string;
        branchName: string;
        /**
         * tất cả query theo branchId này
         * mongooseId
         */
        branchId: string;
        address: string;
        phone: string;
        website?: string;
        bank?: {
            bankName: string;
            accountId: string;
            accountName: string;
            baseQrCode: string;
        };
    };
    isFromAds?: boolean;
};
export type PosInvoiceItem = {
    /**
     * lưu ý về số lượng trong options
     * ví dụ 1 người gọi 2 suất xôi thập cẩm thêm 1 trứng ốp
     * thì khi tính subtotal của options phải là 2 trứng ốp
     *
     * nếu thêm gọi 2 suất xôi thập cẩm thêm 3 trứng ốp
     * thì khi tính subtotal của options phải là 6 trứng ốp
     * => subtotal = option.quantity * item.quantity * option.price
     */
    options?: Array<PosInvoiceItem>;
    _id: string;
    name: string;
    price: number;
    totalCost: number;
    quantity: number;
    subTotal: number;
    total: number;
    discount: number;
    grossProfit?: number;
    grossProfitPct?: number;
    note?: string;
    isCustomItem?: boolean;
    instruction?: string;
    ingredients?: Array<{
        _id: string;
        quantity: number;
        name: string;
        price: number;
        total: number;
    }>;
    thirdPartyItem?: InvoiceThirdPartyItem;
};
