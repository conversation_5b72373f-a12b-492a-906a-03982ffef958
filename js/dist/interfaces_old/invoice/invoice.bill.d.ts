import { Invoice } from './invoice';
export type InvoiceBill = {
    printer: {
        ip: string;
        port: number;
    };
    invoiceId: string;
    thirdPartyInvoiceId?: string;
    date: number;
    address?: string;
    phone?: string;
    website?: string;
    logo?: string;
    items: Array<{
        quantity: string;
        name: string;
        price: string;
        subTotal: string;
    }>;
    qrPayment?: {
        msg: string;
        url: string;
    };
    discounts?: Invoice['billDisplay']['discounts'];
    summarize: Invoice['billDisplay']['summarize'];
    totalPaymentAmount: string;
    customer?: {
        address?: string;
        phone?: string;
    };
    delivery?: {
        distance?: string;
        fee?: string;
        company?: {
            name?: string;
            phoneNumber?: string;
        };
        driver?: {
            name?: string;
            phoneNumber?: string;
        };
    };
    messages?: Array<BillMsg>;
    byeMsg?: string;
    afterByeMessages?: Array<BillMsg>;
    isSelfDelivery: boolean;
};
type BillMsg = {
    msg: string;
    linesAfter: number;
    align: 'left' | 'right' | 'center';
    bold: boolean;
};
export {};
