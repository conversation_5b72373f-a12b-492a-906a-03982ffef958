import { EmployeeDataInRequest } from './employee/employee';
import { PosUpdateData } from './init.pos';
import { InvoiceList, PosListInvoices } from './invoice/invoice.list';
import { StoreConfigs } from './store';
export interface ServerToClientEvents {
    UPDATED_ORDERS: (invoices: InvoiceList) => void;
    UPDATED_POS_DATA: (data: PosUpdateData) => void;
}
export interface ClientToServerEvents {
    join: (room: string, callback: (e: Error) => void) => void;
    leave: (room: string, callback: (e: Error) => void) => void;
    pos_update: (callback: (data: PosUpdateData) => void) => void;
    post_list_invoices: (callback: (data: PosListInvoices) => void) => void;
}
export interface InterServerEvents {
    ping: () => void;
}
export interface SocketData {
    user: EmployeeDataInRequest;
    storeId: string;
    branchId: string;
    store: StoreConfigs;
}
