import { PosInvoice } from '../invoice/invoice.pos';
import { FundName } from './fund';
export type FundData = Array<{
    fundId: FundName;
    fundName: string;
    target: number;
    expected: number;
    received: number;
    debt: number;
    balance: number;
    withdrawl: number;
    remainToTarget: number;
    percentOfTotal: number;
}>;
export type FundOverview = {
    title: string;
    totalBalance: number;
    totalReceived: number;
    totalTarget: number;
    totalDebt: number;
    totalWithdrawl: number;
    totalProfit: number;
    totalNetProfit: number;
    totalAllocated: number;
    totalAdSpend: number;
    data: FundData;
    chartContainerId: string;
    dayoff: number;
    avgSellPerDay: number;
    cost: number;
    purchased: number;
    forecastProfit: number;
    totalRevenue: number;
    costPct: string | number;
    forecastProfitPct: string | number;
    purchasedPct: string | number;
    monthExpenses: number;
    breakEven: {
        pct: number;
        estDay: number;
    };
    payments: {
        [method in PosInvoice['paymentMethod'] | 'app' | 'walkInGuest' | 'ship']?: {
            total: number;
            count: number;
            pct?: number;
        };
    };
};
export type FundViews = {
    month: FundOverview;
    alltime: FundOverview;
};
