import { Modify } from '../common';
import { FinalizeDailyCashflow } from '../finalize_daily_cashflow';
import { FundRecord, MonthlyExpensesSimplify } from './fund';
export type CashflowList = Array<Modify<FinalizeDailyCashflow, {
    totalAdSpend: number;
    allocated: number;
    purchased: number;
    date: string;
    dateShort: string;
    dayStr: string;
    warning?: string;
}>>;
export type PaymentVoucherMonthly = FundRecord & {
    type: 'expenses';
    group: 'EXPENSE_MONTHLY';
    details: {
        data: MonthlyExpensesSimplify;
        note?: string;
        costPerDay: number;
        costPerDayWithoutInitExpense: number;
        total: number;
        changePercentFromPreviousMonth: number;
    };
};
export type InitExpenseConfig = {
    total: number;
    paybackInYears: number;
    costPerDay: number;
    costPerMonth: number;
};
