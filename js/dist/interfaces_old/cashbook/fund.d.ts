import { Modify, Time } from '../common';
import { Allocation } from './allocation';
export type FundBase = {
    fundId: FundName;
};
export type FundStats = {
    target?: number;
    expected: number;
    received: number;
    debt?: number;
    payback?: number;
};
export type FundFullStats = FundStats & {
    balance: number;
};
export type FundRecord = Allocation & {
    _id?: string;
    storeId: string;
    branchId: string;
    invoiceId: string;
    /**
     * dòng tiền out thì expected và received để là -amount
     * để khi sum cho đúng
     */
    type: 'in' | 'out' | 'expenses';
    group: 'DAILY_FINALIZED' | 'MANUAL_INSERT' | 'EXPENSE_MONTHLY' | 'COVER_LOSSES' | 'SUPPLIER_PAYMENT_DEBT' | 'SUPPLIER_PAYMENT_PAYBACK' | 'PURCHASE_BANK_PAYMENT' | 'DEBT_AUTO_WITHDRAWL';
    status: 'completed' | 'cancelled';
    details?: any;
    date: Date;
    time: Time;
    explain?: string;
    tags?: any;
    shiftId?: number;
};
export type PersonalExpenseFundName = 'visa' | 'invest';
export type MonthlyExpenseFundName = 'salary' | 'rent' | 'electric' | 'water' | 'internet' | 'garbage' | 'insurance' | 'init' | 'ads' | 'incurred' | 'manage_app';
export type DailyFlexibleExpenseFundName = 'rice' | 'disposable' | 'debt' | 'drinks';
export type FundName = MonthlyExpenseFundName | DailyFlexibleExpenseFundName | 'profit' | 'others' | 'cash' | 'purchased_bank_payment';
export type MonthlyExpenses = {
    [key in MonthlyExpenseFundName]: {
        target: number;
        expected: number;
    };
};
export type MonthlyExpensesSimplify = {
    [key in MonthlyExpenseFundName]: number;
};
export type PurchaseBankPaymentFund = Modify<FundRecord, {
    fundId: 'purchased_bank_payment';
    group: 'PURCHASE_BANK_PAYMENT';
    details: {
        refPurchase: {
            purchaseId: string;
            status: 'completed' | 'cancelled';
        };
    };
}>;
