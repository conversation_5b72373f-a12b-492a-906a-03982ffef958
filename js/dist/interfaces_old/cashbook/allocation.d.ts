import { Time } from '../common';
import { FinalizeDailyCashflow } from '../finalize_daily_cashflow';
import { FundBase, FundName, FundStats } from './fund';
export type Allocation = FundBase & FundStats & {
    fundName?: string;
};
export type AllocationReturns = {
    allocations: Array<Allocation>;
    notice?: {
        [name in FundName]: Array<string>;
    };
    formErrors?: {
        [name in FundName]: string;
    };
    errors?: string[];
    isManualAllocation?: boolean;
    hasError?: boolean;
};
export type AllocationExpense = {
    fundId: FundName;
    expected: number;
};
export type Allocations = {
    [name in FundName]: Allocation;
};
export type AllocationParams = {
    cashInHand: number;
    netProfit: number;
    expenses: FinalizeDailyCashflow['expenses'];
    time: Time;
};
export type AllocationForm = {
    [name in FundName]: number;
} & {
    date: string;
};
