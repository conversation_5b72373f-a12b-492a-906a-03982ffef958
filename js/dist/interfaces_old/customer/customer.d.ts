import { Place } from '@type/utils/address';
import { ZaloSimplifyUserAccount, ZaloUserAccountInfo, ZaloUserAccountUid } from '../store_account/account_zalo';
export type Customer = {
    _id?: string;
    storeId: string;
    branchId: string;
    shareDataWithBranchIds?: string[];
    name: string;
    phones: Array<CustomerPhone>;
    address?: Array<Place>;
    gender?: 'male' | 'female';
    sources: string[];
    dob?: Date;
    note?: string;
    email?: string[];
    avatar?: string[];
    cover?: string[];
    facebook?: Array<{
        url: string;
        avatar?: string;
        cover?: string;
        name?: string;
        uid?: string;
        source?: string;
    }>;
    group?: Array<{
        url: string;
        name?: string;
        source?: string;
    }>;
    latestUsed: {
        phoneNumber: string;
        address: Place;
    };
    /**
     * customer ko muon nhan tin tu dong
     */
    excludeFromReceiveAutoMsg?: boolean;
    zaloUserData?: Array<ZaloUserAccountInfo>;
    zaloSentFriendRequests?: Array<{
        sentAt: Date;
        from: ZaloSimplifyUserAccount & {
            userId: string;
        };
        to: ZaloSimplifyUserAccount & {
            userId: string;
        };
        success: boolean;
    }>;
    zaloFriends?: Array<CustomerZaloFriend>;
    lastSentZaloFriendRequestAt?: Date;
    lastSentZaloFriendRequestDate?: string;
    lastUnfriendZaloAt?: Date;
    lastUnfriendZaloDate?: string;
    acceptedZaloFriendAt?: Date;
    hasNoZalo?: boolean;
    lastCheckedZaloDataAt?: Date;
    /**
     * để search tiếng việt
     */
    search?: string[];
    rank?: number;
    badges?: Array<{
        type: 'success' | 'danger' | 'primary';
        text: string;
    }>;
    totalSold?: number;
    totalDebt?: number;
    firstOrderedAt?: Date;
    lastOrderedAt?: Date;
    lastRating?: {
        star: number;
        date: Date;
    };
    countOrders?: number;
    queued2Update?: boolean;
    kiotvietId?: number;
    kiotvietData?: any;
    logs?: string[];
};
type CustomerPhone = {
    phoneNumber: string;
    source: string;
    zaloUids?: Array<ZaloUserAccountUid>;
    zaloGlobalId?: string;
    hasZalo?: boolean;
    hasZaloFriend?: boolean;
    zaloName?: string;
    zaloAvatar?: string;
    zaloCover?: string;
    zaloBlockMsgFromStranger?: boolean;
    zaloBlockFriendRequest?: boolean;
    lastCheckedZaloDataAt?: Date;
    lastSentZaloFriendRequestAt?: Date;
    acceptedZaloFriendAt?: Date;
    nextCheckZaloDataAt?: Date;
    nextAutoSentZaloFriendRequestAt?: Date;
    zaloAccountFigures?: Array<ZaloSimplifyUserAccount & {
        userId: string;
        zaloGlobalId: string;
        countSentFriendRequest: number;
        hasFriend: boolean;
    }>;
};
export type CustomerView = Customer & {
    _id: string;
    lastOrderTime?: string;
    phoneView?: Array<CustomerPhone & {
        zaloSentFriendRequests?: Array<{
            date: Date;
            from: string;
        }>;
        friendWith?: {
            name: string;
            phone: string;
            acceptedFriendAt: Date;
        };
    }>;
    addressView?: Array<Place & {
        distance2Store?: Array<{
            storeName: string;
            distance: number;
        }>;
        mapDisplayUrl?: string;
        mapDirectionUrl?: string;
    }>;
    milestones?: Array<{
        text: string;
        time: Date;
    }>;
    logTimeline?: Array<{
        text: string;
        class: string;
    }>;
};
export type CustomerZaloFriend = {
    target: ZaloSimplifyUserAccount & {
        userId: string;
        zaloGlobalId: string;
    };
    friendWith: ZaloSimplifyUserAccount & {
        userId: string;
        zaloGlobalId: string;
    };
    acceptedFriendAt?: Date;
    unfriendAt?: Date;
};
export {};
