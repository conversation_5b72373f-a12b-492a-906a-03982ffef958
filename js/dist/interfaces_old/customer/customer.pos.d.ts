import { Place } from '@type/utils/address';
export type CustomerAutocompleteRecord = {
    name?: string;
    phoneNumber: string;
    address: Place;
    latestUsedAddress: Place;
    _id: string;
    facebook?: string;
    allAddress: Place[];
    totalSold: number;
    countOrders: number;
    gender?: string;
    avatar?: string;
    note?: string;
    /**
     * chỉ có điện thoại nhập vào là exact
     */
    exactMatch?: boolean;
    /**
     * exact text(tên, địa chỉ)
     * nhưng chưa chắc đã đúng ý
     * vẫn phải có options tạo mới
     */
    semiExactMatch?: boolean;
    /**
     * create new record
     */
    createNewUserWith?: 'ADDRESS' | 'NAME' | 'PHONE' | string;
    text?: string;
    value?: string;
};
export type CustomerCreateOption = {
    createNewUserWith: 'ADDRESS' | 'NAME' | 'PHONE' | string;
    text: string;
    value: string;
    name?: string;
    phoneNumber?: string;
    address?: Place;
};
export type CustomerAutocompleteResult = Array<CustomerAutocompleteRecord | CustomerCreateOption>;
