import { PosInvoiceItem } from '../invoice/invoice.pos';
export type SimplifyProduct = {
    _id?: string;
    /**
     * khi merge product se co originId
     */
    originId?: string;
    name: string;
    price: number;
    category: string;
    isActive?: boolean;
    isForSale?: boolean;
    allowsSale?: boolean;
    isDeleted?: boolean;
    /**
     * lần cuối nhập hàng
     */
    lastPurchasedAt?: Date;
    /**
     * tính giá vốn
     */
    cost: number;
    ingredients?: Array<{
        _id: string;
        name: string;
        price: number;
        quantity: number;
        total: number;
    }>;
    instruction?: string;
    measureUnit?: string;
    /**
     * phân biệt đồ uống, bữa ăn và đồ đóng hộp
     */
    isMeal?: boolean;
    isDrink?: boolean;
    isCanned?: boolean;
    /**
     * chỉ để tính cost, ko show ra phần nguyên liệu nhập vào hàng ngày
     */
    calcCostOnly?: boolean;
    /**
     * định lượng mặc định để tính cost cho món custom
     */
    customItemDefaultQuantityToCalcCost?: number;
    /**
     * KIỂM KHO
     */
    checkInventoryPeriodically?: {
        /**
         * bao nhiêu ngày thì kiểm tra định kỳ 1 lần
         */
        cycle: 'shift' | 'day';
        interval?: number;
    };
    manualCheckInventory?: {
        /**
         * bao nhiêu ngày thì kiểm tra 1 lần
         */
        cycle: 'shift' | 'day';
        interval?: number;
    };
    /**
     * kiểm kho tự động / liên kết kiểm kho:
     * bán ra 1 hàng thì trừ x hàng trong kho
     * ví dụ:
     *  bán xôi thập cẩm tự trừ trứng cút, hành khô...
     *  bán 1 lon nước c2 tự động trừ luôn lon c2 đấy
     *  bán 1 cái túi lv trừ 1 cái túi lv
     */
    isAutoExportInventory?: boolean;
    linkedInventoryItems?: Array<{
        _id: string;
        exportAmount: number;
        /**
         * có 2 loại:
         *  1 loại export với số lượng chắc chắn như:
         *    bán 1 cái túi lv trừ 1 cái túi lv
         *  1 loại của hàng ăn với số lượng ko chắc chắn như:
         *    bán 1 bát phở - 1 lạng thịt
         * -> thịt còn lại trong kho lại ít hơn
         *
         * để gửi cảnh báo khi một mặt hàng bị chênh lệch quá nhiều
         *
         * thịt nhập vào x số lượng nhưng thành phẩm ra lại y số lượng
         * thịt nhập vào x số lượng nhưng họ chỉ chế biến y số lượng,
         * ra thành phẩm z số lượng, tồn kho k số lượng
         */
        isExactAmount: boolean;
        /**
         * sai số cho phép trước khi gửi cảnh báo chênh lệch
         */
        permissibleError?: number;
    }>;
    inventoryNote?: string;
    supplierIds?: string[];
    kiotvietId?: number;
    kiotvietIds?: number[];
    grabIds?: string[];
    shopeeIds?: number[];
    mergedToId?: string;
};
export type Product = SimplifyProduct & {
    storeId: string;
    branchId: string;
    order?: number;
    kiotvietData?: any;
    shopeeData?: ThirdPartyProduct[];
    grabData?: ThirdPartyProduct[];
    logs?: string[];
};
export type CachedAllThirdPartyProducts = {
    [thirdPartyProductId: number | string]: SimplifyProduct;
};
export type ThirdPartyProduct = {
    name: string;
    price: number;
    category: string;
    id: string | number;
    parentGroupId?: string | number;
    desc: string;
    imageUrls: string[];
    imageUrl: string;
};
export type PosProduct = {
    _id: string;
    price: number;
    cost: number;
    name: string;
    category: string;
    editable: boolean;
    canChooseOptions: boolean;
    instruction?: string;
    active?: boolean;
    optionProducts?: PosProduct[];
    selectedItem?: PosInvoiceItem;
    hasCloneAnotherProduct?: boolean;
};
export type PosProducts = Array<PosProduct>;
