import { Alert } from 'ndx_alert_errors';
import { Place } from '@type/utils/address';
import { Modify } from './common';
export type StoreQuery = {
    storeId: string;
    branchId: string;
};
export type StoreConfigs = {
    name: string;
    /**
     * mongooseId
     */
    storeId: string;
    branchName: string;
    /**
     * tất cả query theo branchId này
     * mongooseId
     */
    branchId: string;
    shareDataWithBranchIds?: string[];
    address: string;
    addressInfo: Place & {
        region: string;
        regionName: string;
        /**
         * match voi SearchPlaceParams trong ndx_maps searchPlaceByAddress
         */
        regionSearch: string;
    };
    openingDate: Date;
    /**
     * hiện ra trong bill
     */
    phone: string;
    website?: string;
    bank?: {
        bankName: string;
        accountId: string;
        accountName: string;
        baseQrCode: string;
    };
    networkPrinter?: {
        ip: string;
        port: number;
    };
    bill?: {
        logo?: string;
        byeMsg?: string;
    };
    /**
     * third party
     */
    grab?: {
        merchantId: string;
        advertiserId: string;
        /**
         * để lấy payment
         * nhấn vào grab -> finance
         * trên url
         * https://merchant.grab.com/finances?page=st&startDate=26/6/24&endDate=21/11/24&grabID=518f3079-dcf7-40ea-bc65-26e822f6c06f
         */
        grabId: string;
        auth: {
            username: string;
            password: string;
        };
        commissionRate: number;
        /**
         * tự động tăng ngân sách vào các ngày cố định
         */
        autoIncreaseBudgets?: Array<{
            campaignID: string;
            day: number;
            increaseToBudget: number;
            normalBudget: number;
        }>;
    };
    shopee?: {
        merchantId: string;
        restaurantId: string;
        auth: {
            username: string;
            password: string;
            hashedPassword: string;
        };
    };
    kiotviet?: {
        retailer: string;
        auth: {
            username: string;
            password: string;
        };
    };
    initCost?: {
        total: number;
        paybackInYears: number;
    };
    facebook: {
        ads?: {
            tax: number;
        };
    };
    /**
     * gui report về mail/discord
     */
    reportAlertConfigs?: Alert['opts']['configs'];
};
export type NewStoreConfigs = Modify<StoreConfigs, {
    storeId?: string;
    branchId?: string;
    /**
     * chi nhánh mới từ store cũ
     */
    isNewBranch?: boolean;
    /**
     * share dữ liệu: customer, product, supplier
     * với 1 chi nhánh cùng hệ thống
     */
    shareDataWithBranchId?: string;
    owner: {
        _id?: string;
        name: string;
        email: string;
        password: string;
    };
}>;
