/**
 * https://stackoverflow.com/questions/********/how-to-implement-the-publish-subscribe-pattern-in-typescript
 */
import { Time } from './common';
import { PosUpdateData } from './init.pos';
import { InvoiceList } from './invoice/invoice.list';
import { StoreQuery } from './store';
import { ZaloStoreAccount } from './store_account/account_zalo';
/**
 * Defines the function type of the publish function.
 *
 * Extracts the keys from `E` as valid event types, and the matching
 * property as the payload.
 */
export type PubTypeFn<E> = <Key extends string & keyof E>(event: Key, message: E[Key]) => void;
/**
* Defines the function type for the subscribe function.
*
* Extracts the keys from `E` as valid event types, and the matching
* property as the payload to the callback function.
*/
export type SubTypeFn<E> = <Key extends string & keyof E>(event: Key, fn: (message: E[Key]) => void) => void;
export type UnsubTypeFn<E> = <Key extends string & keyof E>(event: Key, index: number) => void;
/**
* Defines the function type for the subscription callback. Ensures
* the message payload is a valid property of the event being used.
*/
/**
* Tie everything together.
*/
export type PubSubType<E> = {
    publish: PubTypeFn<E>;
    subscribe: SubTypeFn<E>;
    unsubscribe: UnsubTypeFn<E>;
};
/**
* Creates a new PubSub instance, the `E` type parameter should be a
* type enumerating all the available events and their payloads.
*
* @example
* type Events = {
*  warn: { message: string },
*  error: { message: string }
* }
*
* const pubSub = PubSub<Events>()
* pubSub.publish('warn', { message: "Something bad happened!" })
*/
export type PubSubEvents = {
    refinalizeDay: {
        store: StoreQuery;
        time: Time;
    };
    refinalizeMonth: {
        store: StoreQuery;
        time: Time;
    };
    customerDetails: {
        store: StoreQuery;
        _id: string;
    };
    recacheProduct: StoreQuery;
    zaloAccountChange: {
        store: StoreQuery;
        account: ZaloStoreAccount;
        type: 'main' | 'secondary';
    };
    UPDATED_ORDERS: {
        store: StoreQuery;
        data: InvoiceList;
    };
    UPDATED_POS_DATA: {
        store: StoreQuery;
        data: PosUpdateData;
    };
};
