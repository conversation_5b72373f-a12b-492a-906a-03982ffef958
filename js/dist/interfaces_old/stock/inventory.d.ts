/**
 * kiểm kho thủ công:
 *  các mặt hàng ko thể kiểm kho tự động như gạo, xôi, thịt
 *
 * kiểm kho tự động / liên kết kiểm kho:
 *  bán ra 1 hàng thì trừ x hàng trong kho
 *  ví dụ:
 *   bán xôi thập cẩm tự trừ trứng cút, bát giấ<PERSON>, hành khô...
 *   bán 1 lon nước c2 tự động trừ luôn lon c2 đấy
 *   bán 1 cái túi lv trừ 1 cái túi lv
 */
export type Inventory = {
    storeId: string;
    branchId: string;
    transactionType: 'import' | 'export' | 'check';
    product: {
        _id: string;
        price: number;
        cost: number;
        name: string;
    };
    invoices: Array<{
        _id: string;
        quantity: number;
        total: number;
        remain: {
            quantity: number;
            total: number;
        };
    }>;
    shifts: Array<{
        _id: string;
        shiftId: string;
    }>;
    /**
     * export = âm
     * import = dương
     */
    invoiceSummarize: {
        totalQuantity: number;
        totalAmount: number;
    };
    totalRemainQuantity: number;
    totalRemainAmount: number;
    date: Date;
    dateStr: String;
    time: {
        date: number;
        month: number;
        year: number;
    };
};
