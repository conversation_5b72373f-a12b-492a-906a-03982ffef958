import { Modify, Time } from '../common';
import { SimplifyProduct } from '../product/product';
import { PosPurchasedInvoice, PosPurchasedItem } from './purchased.pos';
export type PurchasedInvoiceItem = PosPurchasedItem & {
    product: SimplifyProduct;
    images?: string[];
    priceChange?: number;
    priceChangePct?: number;
};
export type PurchasedInvoice = Modify<PosPurchasedInvoice, {
    storeId: string;
    branchId: string;
    purchaseId: string;
    laterPayments?: Array<PurchasedInvoiceLaterPayment>;
    finalizedPayment: {
        paidAmount: number;
        debt: number;
    };
    items: Array<PurchasedInvoiceItem>;
    status: 'cancelled' | 'completed' | 'draft';
    refReceiptIds?: string[];
    refBankPaymentReceiptId?: string;
    purchasedAt: Date;
    shiftId: number;
    time: Time;
    logs?: string[];
    kiotvietData?: any;
}>;
export type PurchasedInvoiceLaterPayment = {
    refReceiptId: string;
    paidAmount: number;
    debt: number;
    paidAt: Date;
    status: 'completed' | 'cancelled';
    paymentMethod: 'cash' | 'bank_transfer';
};
