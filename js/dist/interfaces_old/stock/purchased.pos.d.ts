import { SimplifyProduct } from '../product/product';
import { PurchasedInvoiceLaterPayment } from './purchased_order';
export type PurchaseProduct = SimplifyProduct & {
    _id: string;
    /**
     * de search
     */
    namevn?: string;
};
export type PosPurchasedItem = {
    _id: string;
    name: string;
    price: number;
    quantity: number;
    subTotal: number;
    discount?: number;
    total: number;
    note: string;
    lastPurchasedAt?: Date;
};
export type PosPuchaseSupplier = {
    kiotvietId?: number;
    name: string;
    _id: string;
    /**
     * ko dau de search
     */
    namevn?: string;
};
export type PosPurchasedInvoice = {
    purchaseId?: string;
    posItems: {
        [_id: string]: PosPurchasedItem;
    };
    items: PosPurchasedItem[];
    supplier: PosPuchaseSupplier;
    /**
     * pay lúc đầu
     */
    initPayment: PurchasedInvoiceLaterPayment;
    summarize: {
        total: number;
        totalDiscount: number;
        discountRatio: number;
        subTotal: number;
        totalQuantity: number;
        totalItems: number;
    };
    purchasedAt: Date;
    shiftId: number;
    /**
     * phiếu công nợ
     */
    debtVoucherImages?: string[];
};
export type PosPurchasedSummarize = {
    invoice: PosPurchasedInvoice;
    noSupplierId: string;
};
export type PosPurchasedSummarizeDismissData = {
    isSaved?: boolean;
    isCancelled?: boolean;
};
