import { Types } from 'mongoose';
import { DeviceTokenOnServer } from '../device_token';
import { Time } from '../common';
export type EmployeeDataInRequest = {
    _id: string | typeof Types.ObjectId;
    uid: string;
    email: string;
    position: string[];
    isOwner: boolean;
    isStoreManager: boolean;
    isActive: boolean;
    /**
     * 1 owner có thể sở hữu nhiều nhà hàng
     */
    stores: Array<{
        name: string;
        storeId: string;
        /**
         * 1 owner có thể quản lý nhiều branch
         * nhưng thu ngân hoặc nhân viên thì chỉ 1
         */
        branchIds: string[];
    }>;
};
export type Employee = EmployeeDataInRequest & {
    password: string;
    name: string;
    salary: number;
    allowance: number;
    startWorkingDate: Date;
    stopWorkingDate?: Date;
    profilePictures?: Array<{
        _id: string;
        url: string;
    }>;
    profileIdPictures?: Array<{
        _id: string;
        url: string;
    }>;
    deviceTokens?: Array<DeviceTokenOnServer>;
    activeDeviceTokens?: Array<DeviceTokenOnServer>;
};
export type EmployeeTask = {
    storeId: string;
    branchId: string;
    position: string;
    name: string;
    block: number;
    fine: number;
    fineLevel: string;
    order: number;
    _id?: string;
};
export type EmployeeTicket = {
    storeId: string;
    branchId: string;
    employee: string;
    type: string;
    value: number;
    content: string;
    date: Date;
    _id?: string;
    time: Time;
};
export type EmployeeRule = {
    storeId: string;
    branchId: string;
    position: string;
    name: string;
    order: number;
    _id?: string;
};
