import { Time } from '../common';
import { PosReport } from './report.pos';
import { ProductStats } from './stats';
export type SaleStats = {
    totalRevenue: number;
    totalExpenses: number;
    grossProfit: number;
    totalCount: number;
    totalAdSpend: number;
    grabAdSpend: number;
    netRevenue: number;
    payment: {
        cash: {
            total: number;
            count: number;
        };
        bank: {
            total: number;
            count: number;
        };
        grab: {
            total: number;
            count: number;
        };
        shopee: {
            total: number;
            count: number;
        };
    };
};
export type XmReport = SaleStats & {
    type: 'hour' | 'day' | 'month';
    /**
     * so với giờ trc, ngày trc, tháng trc
     */
    changePercent: number;
    sales: ProductStats;
    isFinalized: boolean;
    time: Time;
    date: Date;
    details?: any;
    tags?: any;
    logs?: string[];
};
export type CashStats = SaleStats & {
    shifts?: PosReport['cashStats'];
};
export type PurchasedReport = {
    totalDebt: number;
    totalPurchased: number;
    payment: {
        cash: number;
        bank: number;
    };
    shifts: PosReport['purchaseOverview'];
};
