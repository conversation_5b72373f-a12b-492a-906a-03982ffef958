import { PosShiftEndForm } from '../shift/shift.pos';
export type PosReport = {
    shifts: Array<{
        _id?: string;
        shiftId: number;
        explain: string;
        isFinalized: boolean;
    }>;
    cashStats: {
        [shiftId: number]: {
            shiftId: number;
            totalRevenue: number;
            totalExpenses: number;
            grossProfit: number;
            totalCount: number;
            payment: {
                cash: {
                    total: number;
                    count: number;
                };
                bank: {
                    total: number;
                    count: number;
                };
                grab: {
                    total: number;
                    count: number;
                };
                shopee: {
                    total: number;
                    count: number;
                };
            };
        };
    };
    purchaseOverview: {
        [shiftId: number]: {
            totalDebt: number;
            totalPurchased: number;
            payment: {
                cash: number;
                bank: number;
            };
            suppliers: {
                [supplierId: string]: {
                    name: string;
                    debt: number;
                    total: number;
                };
            };
        };
    };
    purchases: {
        [shiftId: number]: {
            [supplierId: string]: {
                supplierName?: string;
                debt: number;
                total: number;
                payment: {
                    cash: number;
                    bank: number;
                };
                invoices: Array<{
                    purchaseId: string;
                    desc: string;
                    images: string[];
                    items: Array<{
                        name: string;
                        quantity: number;
                    }>;
                }>;
                hasAllDebtImage: boolean;
                items: Array<PosReportPurchaseItem>;
            };
        };
    };
    otherExpenses: {
        [shiftId: number]: {
            total: number;
            items?: Array<{
                amount: number;
                explain: string;
            }>;
        };
    };
    endShifts: {
        [shiftId: number]: PosShiftEndForm;
    };
};
export type PosReportPurchaseItem = {
    _id: string;
    purchaseId: string;
    name: string;
    price: number;
    quantity: number;
    total: number;
    images?: string[];
    priceChange?: number;
    priceChangePct?: number;
};
