import { PurchasedInvoice } from '../stock/purchased_order';
export type ProductPurchaseData = {
    product: {
        _id: string;
        name: string;
    };
    dateRange: {
        start: string;
        end: string;
    };
    chart: {
        series: Array<{
            name: string;
            type: 'column' | 'line';
            data: number[];
            color?: string;
        }>;
        labels: string[];
        /**
         * chỉ hiện ngày, lúc rê chuột qua
         * tooltip mới hiện labels
         */
        shortLabels: string[];
    };
    overview?: {
        total: string;
        quantity: string;
        avgUsedPerDay: {
            quantity: number;
            total: string;
        };
    };
};
export type ProductSellData = {
    chart: {
        series: Array<{
            name: string;
            type: 'column' | 'line';
            data: number[];
            color?: string;
        }>;
        labels: string[];
        /**
         * chỉ hiện ngày, lúc rê chuột qua
         * tooltip mới hiện labels
         */
        shortLabels: string[];
    };
};
export type PurchaseData = {
    overview: {
        total: number;
        paidAmount: number;
        debt: number;
    };
    list?: Array<PurchasedInvoice & {
        dateStr: string;
    }>;
    chart: {
        start: Date;
        end: Date;
        overview: {
            total: string;
            avg: string;
        };
        purchaseOverview: {
            series: Array<{
                name: string;
                type: 'column';
                data: number[];
            }>;
            labels: string[];
            /**
             * chỉ hiện ngày, lúc rê chuột qua
             * tooltip mới hiện labels
             */
            shortLabels: string[];
        };
        topPurchases: Array<{
            _id: string;
            name: string;
            total: number;
            count: number;
            quantity: number;
        }>;
    };
};
