import { Product } from './product/product';
export type KiotvietRequestConfigs = {
    token: string;
    cookie: string;
    branchId: string;
};
export type KiotvietProduct = {
    'GroupProductType': 0;
    'UnitListStr': '';
    'MasterCode': 'SPC000097';
    'VariantCount': 0;
    'Id': 1754994;
    'ProductGroup': 3;
    'ProductType': 2;
    'CategoryId': 190924;
    'CategoryName': 'Chi phí khác';
    'CategoryNameTree': 'Chi phí khác';
    'isActive': true;
    'HasVariants': false;
    'AllowsSale': false;
    'isDeleted': false;
    'Name': 'So điện';
    'FullName': 'So điện';
    'Code': 'SP000140';
    'Description': '';
    'BasePrice': 0;
    'Cost': 130000;
    'LatestPurchasePrice': 130000;
    'OnHand': 1;
    'OnHandCompareMin': -1;
    'OnHandCompareMax': -999999998;
    'CompareOnHand': 0;
    'CompareCost': 0;
    'CompareBasePrice': 0;
    'CompareProductGroup': 0;
    'Reserved': 0;
    'MinQuantity': 0;
    'MaxQuantity': 999999999;
    'CustomId': 0;
    'CustomValue': 0;
    'Unit': '';
    'ConversionValue': 1;
    'OrderTemplate': '';
    'IsTopping': false;
    'IsLotSerialControl': false;
    'CreatedDate': '0001-01-01T00:00:00.0000000';
    'IsTimeType': false;
    'HaveProcessedGoodsChild': false;
};
export type KiotvietInvoice = {
    InvoiceDetails: KiotvietInvoiceDetails;
    'Id': 16539200;
    'OrderId': 16935243;
    'PurchaseDate': '2023-02-11T09:09:04.3830000';
    'CreatedDate': '2023-02-11T09:09:04.4000000';
    'CreatedBy': 40723;
    'RetailerId': *********;
    'Code': 'HD002264';
    'Discount': 0;
    'Status': 1;
    'BranchId': 17988;
    'SoldById': 40723;
    'Total': 20000;
    'TotalPayment': 20000;
    'UsingCod': 0;
    'Surcharge': 0;
    'NewInvoiceTotal': 20000;
    'EntryDate': '2023-02-11T09:08:47.0000000';
    'HistoryNote': '';
    'SaleChannelId': 0;
    'PayingAmount': 20000;
    'DiningOption': 3;
    'Payments': [];
    'Returns': [];
    'InvoiceDeliveries': [];
    'InvoiceDeliveryTrackings': [];
    'InvoiceOrderSurcharges': [];
    'Returns1': [];
    'InvoicePromotions': [];
    'SaleChannel': {
        'Id': 0;
        'CreatedDate': '0001-01-01T00:00:00.0000000';
        'CreatedBy': 0;
        'RetailerId': 0;
        'Name': 'Khách đến trực tiếp';
        'IsActive': false;
        'Position': 0;
        'Img': 'fa fas fa-shopping-basket';
        'Invoices': [];
        'Orders': [];
        'Returns': [];
    };
    'DeliveryInfoes': [];
    'DeliveryPackages': [];
    'DeliveryPayments': [];
    'CustomerName': '';
    'CustomerEmail': '';
    'CustomerContactNumber': '';
    'CustomerAddress': '';
    'CustomerLocationName': '';
    'CustomerWardName': '';
    'SoldByGivenName': '';
    'CreatedByGivenName': '';
    'TableAndRoomName': 'Mang về';
    'OrderCode': '';
    'CustomerPoint': 0;
    'CustomerOldPoint': 0;
    'CustomerCode': '';
    'PaidAmount': 20000;
    'PaymentId': 0;
    'PaymentMethod': '';
    'StatusValue': 'Hoàn thành';
    'SubTotal': 20000;
    'TotalQuantity': 0;
    'TotalQuantityWoTopping': 0;
    'CompareSoldById': 40723;
    'ComparePurchaseDate': '2023-02-11T09:09:04.3830000';
    'CompareStatus': 1;
    'IsHavePurchasePayment': false;
};
export type KiotvietInvoiceDetails = Array<{
    'Id': 45798637;
    'InvoiceId': 16518653;
    'ProductId': 1081990;
    'Quantity': 2;
    'Price': 35000;
    'Tax': 0;
    'Discount': 0;
    'DiscountRatio': 0;
    'CreatedDate': '2023-02-11T06:56:12.8500000';
    'ViewDiscount': 0;
    'Uuid': '72f28592-c44e-4216-8994-a96a7c929723';
    'Rank': 1;
    'RetailerId': *********;
    'Product': {
        'Id': 1081990;
        'Code': 'SP000005';
        'Name': 'Xôi Gà Đảo Nấm Hương';
        'CategoryId': 117625;
        'AllowsSale': true;
        'BasePrice': 35000;
        'Tax': 0;
        'RetailerId': *********;
        'isActive': true;
        'CreatedDate': '2022-11-26T08:31:19.3000000';
        'ProductType': 3;
        'HasVariants': false;
        'Unit': '';
        'ConversionValue': 1;
        'FullName': 'Xôi Gà Đảo Nấm Hương';
        'IsTimeServices': false;
        'IsLotSerialControl': false;
        'IsRewardPoint': false;
        'isDeleted': false;
        'IsTimeType': false;
        'IsTopping': false;
        'ProductGroup': 1;
        'MasterCode': 'SPC20221126831199595';
        'Revision': 'AAAAABtON30=';
        'Formula': [];
        'ProductImages': [];
        'PurchaseReturnDetails': [];
        'ProductChild': [];
        'ProductAttributes': [];
        'ProductChildUnit': [];
        'PriceBookDetails': [];
        'ProductBranches': [];
        'TableAndRooms': [];
        'Manufacturings': [];
        'ManufacturingDetails': [];
        'DamageDetails': [];
        'ProductSerials': [];
        'ProductFormulaHistories': [];
        'ProductToppings': [];
        'Topping': [];
        'ProductNoteTemplates': [];
        'ProductProcessingSector': [];
        'CancelDishReasonDetails': [];
        'NotifyDishes': [];
        'ProductShelves': [];
    };
    'SubTotal': 70000;
    'ProductCode': 'SP000005';
    'ProductName': 'Xôi Gà Đảo Nấm Hương';
    'ProductSName': 'Xôi Gà Đảo Nấm Hương';
    'ProductGroup': 1;
    'Image': '';
    'ProductAttributeLabel': '';
    'ProductShelvesStr': '';
    'CategoryTree': 'Món Signatures';
    'IsProcessedGoods': false;
}>;
export type KiotvietCustomer = {
    __type: 'KiotViet.Services.Interface.CustomerWithInvoiced, KiotViet.Services';
    Name: 'Khách Lẻ';
    Debt: 0;
    Id: 238202;
    BranchId: 17988;
    BranchName: 'Chi nhánh trung tâm';
    Code: 'KH000229';
    ContactNumber: '0973659232';
    SearchNumber: '0973659232';
    WardName: '';
    TotalInvoiced: 70000;
    TotalRevenue: 70000;
    TotalPoint: 0;
    CreatedDate: '2023-02-06T11:36:50.7730000';
    Email: '';
    Address: 'Bệnh Viện Ung Bướu';
    Groups: '';
    Type: 0;
    IsActive: true;
    IsDeleted: false;
    Organization: '';
    RetailerId: *********;
    LastTradingDate: '2023-02-06T11:37:27.9670000';
    CustomerType: 'Cá nhân';
    CreatedByName: 'Lê Thị Thu Thảo';
};
export type KiotvietPurchasedOrderDetails = Array<{
    'ProductName': 'Sữa tươi ko đường';
    'ProductCode': 'SP000130';
    'ReturnQuantity': 0;
    'SubTotal': 21900;
    'ProductImage': '';
    'ProductAttributeLabel': '';
    'CategoryTree': 'Nguyên liệu';
    'ProductShelvesStr': '';
    'Id': 1126209;
    'PurchaseId': 476744;
    'ProductId': 1469855;
    'Quantity': 3;
    'Price': 7300;
    'Discount': 0;
    'CreatedDate': '2023-02-08T07:43:23.5700000';
    'Description': '';
    'Allocation': 0;
    'OrderByNumber': 3;
    'AllocationSuppliers': 0;
    'AllocationThirdParty': 0;
    'Product': Product;
}>;
export type KiotvietPurchasedOrder = {
    'Id': 503928;
    'Branch': {
        'Id': 17988;
        'Name': 'Chi nhánh trung tâm';
        'Type': 0;
        'Address': '150 Trường Thi';
        'Province': 'Thanh Hóa';
        'District': 'Thành phố Thanh Hóa';
        'ContactNumber': '0916999192';
        'IsActive': true;
        'RetailerId': *********;
        'CreatedBy': 0;
        'Email': '<EMAIL>';
        'LimitAccess': false;
        'LocationName': 'Thanh Hóa - Thành phố Thanh Hóa';
        'WardName': 'Phường Trường Thi';
        'isAcceptBookClosing': false;
        'GmbStatus': 1;
        'Orders': [];
        'Transfers1': [];
        'DamageItems': [];
        'SurchargeBranches': [];
        'AdrApplications': [];
        'Customers': [];
        'Suppliers': [];
        'ExpensesOtherBranches': [];
        'PriceBookDiningOptions': [];
        'ProcessingSectors': [];
        'CancelDishReasonDetails': [];
        'CancelDishReasons': [];
        'Reservation': [];
        'NotifyDishes': [];
        'GroupNoteTemplates': [];
        'NoteTemplates': [];
        'PayslipPayments': [];
        'PayslipPaymentAllocations': [];
        'BranchTakingAddresses': [];
        'TotalUser': 0;
        'CompareBranchName': 'Chi nhánh trung tâm';
        'IsTimeSheetException': false;
    };
    'BranchId': 17988;
    'Code': 'PN000428';
    'CompareCode': 'PN000428';
    'CompareDiscount': 0;
    'ComparePurchaseDate': '2023-02-17T17:20:57.3170000';
    'CompareStatusValue': 'Đã nhập hàng';
    'CompareSupplierId': 27253;
    'CompareUserId': 40723;
    'CreatedBy': 40723;
    'CreatedDate': '2023-02-17T17:20:57.3370000';
    'Description': '';
    'Discount': 0;
    'DiscountRatio': 0;
    'ExReturnSuppliers': 0;
    'ExReturnThirdParty': 0;
    'PaidAmount': 0;
    'PurchaseDate': '2023-02-17T10:20:57.317Z';
    'PurchaseOrderDetails': [];
    'PurchaseOrderExpensesOthers': [];
    'PurchasePayments': [];
    'PurchaseReturns': [];
    'RetailerId': *********;
    'Status': 3;
    'StatusValue': 'Đã nhập hàng';
    'SubTotal': 743500;
    'Supplier': {
        'TotalInvoiced': 0;
        'CompareCode': 'NCC000011';
        'CompareName': 'Quý (Hằng Nga)';
        'ComparePhone': '0394774367';
        'Id': 27253;
        'Name': 'Quý (Hằng Nga)';
        'Phone': '0394774367';
        'RetailerId': *********;
        'Code': 'NCC000011';
        'CreatedDate': '2022-12-06T07:17:58.6430000';
        'CreatedBy': 40723;
        'ModifiedDate': '2023-02-17T17:14:52.5800000';
        'ModifiedBy': 40723;
        'Debt': 2329000;
        'LocationName': '';
        'WardName': '';
        'BranchId': 17988;
        'isDeleted': false;
        'isActive': true;
        'SearchNumber': '0394774367';
        'PurchaseOrders': [];
        'PurchaseReturns': [];
        'PurchasePayments': [];
        'SupplierGroupDetails': [];
    };
    'SupplierDebt': 0;
    'SupplierId': 27253;
    'SupplierOldDebt': 0;
    'ToComplete': false;
    'Total': 743500;
    'TotalProductType': 2;
    'TotalQuantity': 7.55;
    'User': {
        'CompareGivenName': 'Lê Thị Thu Thảo';
        'CompareIsLimitedByTrans': false;
        'CompareIsShowSumRow': true;
        'CompareUserName': 'admin';
        'IsTimeSheetException': false;
        'Id': 40723;
        'Email': '';
        'GivenName': 'Lê Thị Thu Thảo';
        'MobilePhone': '0916999192';
        'CreatedDate': '2022-11-26T08:08:28.1200000';
        'IsActive': true;
        'IsAdmin': true;
        'RetailerId': *********;
        'UserName': 'admin';
        'Type': 0;
        'CreatedBy': 0;
        'CanAccessAnySite': false;
        'isDeleted': false;
        'GroupId': 0;
        'Permissions': [];
        'Apps': [];
        'Invoices': [];
        'Transfers1': [];
        'PriceBookUsers': [];
        'CashFlows': [];
        'Invoices1': [];
        'Orders': [];
        'Returns1': [];
        'Manufacturings': [];
        'DamageItems': [];
        'TokenApis': [];
        'SmsEmailTemplates': [];
        'BalanceAdjustments1': [];
        'PointAdjustments': [];
        'PointAdjustmentsCreatedBy': [];
        'Devices': [];
        'NotificationSettings': [];
        'DamageItems1': [];
        'PurchaseOrders1': [];
        'PurchaseReturns1': [];
        'CostAdjustments': [];
        'ImportExportFiles': [];
        'ProcessingSectors': [];
        'CancelDishReasonDetails': [];
        'CancelDishReasons': [];
        'Reservation': [];
        'NotifyDishes': [];
        'NotifyDishes1': [];
        'GroupNoteTemplates': [];
        'NoteTemplates': [];
        'PayslipPayments': [];
        'PayslipPaymentAllocations': [];
        'UserDevices': [];
    };
    'User1': {
        'CompareGivenName': 'Lê Thị Thu Thảo';
        'CompareIsLimitedByTrans': false;
        'CompareIsShowSumRow': true;
        'CompareUserName': 'admin';
        'IsTimeSheetException': false;
        'Id': 40723;
        'Email': '';
        'GivenName': 'Lê Thị Thu Thảo';
        'MobilePhone': '0916999192';
        'CreatedDate': '2022-11-26T08:08:28.1200000';
        'IsActive': true;
        'IsAdmin': true;
        'RetailerId': *********;
        'UserName': 'admin';
        'Type': 0;
        'CreatedBy': 0;
        'CanAccessAnySite': false;
        'isDeleted': false;
        'GroupId': 0;
        'Permissions': [];
        'Apps': [];
        'Invoices': [];
        'Transfers1': [];
        'PriceBookUsers': [];
        'CashFlows': [];
        'Invoices1': [];
        'Orders': [];
        'Returns1': [];
        'Manufacturings': [];
        'DamageItems': [];
        'TokenApis': [];
        'SmsEmailTemplates': [];
        'BalanceAdjustments1': [];
        'PointAdjustments': [];
        'PointAdjustmentsCreatedBy': [];
        'Devices': [];
        'NotificationSettings': [];
        'DamageItems1': [];
        'PurchaseOrders1': [];
        'PurchaseReturns1': [];
        'CostAdjustments': [];
        'ImportExportFiles': [];
        'ProcessingSectors': [];
        'CancelDishReasonDetails': [];
        'CancelDishReasons': [];
        'Reservation': [];
        'NotifyDishes': [];
        'NotifyDishes1': [];
        'GroupNoteTemplates': [];
        'NoteTemplates': [];
        'PayslipPayments': [];
        'PayslipPaymentAllocations': [];
        'UserDevices': [];
    };
    'UserId': 40723;
    'ViewPrice': true;
    'Debt': 743500;
    details: KiotvietPurchasedOrderDetails;
};
export type KiotvietSupplierDebt = {
    '__type': 'KiotViet.Services.Interface.GroupedBalanceTrack, KiotViet.Services';
    'PartnerId': 27253;
    'DocumentId': 430453;
    'DocumentCode': 'PCPN000423';
    'DocumentType': 0;
    'Value': 352000;
    'Balance': -2242500;
    'RetailerId': 0;
    'TransDate': '2023-02-17T07:24:46.2900000';
    'DataZone': 1;
};
export type KiotvietSupplier = {
    '__type': 'KiotViet.Services.Interface.SupplierWithInvoiced, KiotViet.Services';
    'Name': 'Hào Nga bát đũa thìa';
    'Debt': 0;
    'Id': 37119;
    'Code': 'NCC000013';
    'Phone': '0988338336';
    'SearchNumber': '0988338336';
    'Address': '234 tống duy tân';
    'LocationName': '';
    'WardName': '';
    'TotalInvoiced': 0;
    'TotalReturn': 0;
    'TotalInvoicedWithoutReturn': 0;
    'CreatedDate': '2023-01-16T18:40:05.6300000';
    'Groups': '';
    'isActive': true;
    debt: Array<KiotvietSupplierDebt>;
};
