import { FundRecord } from '../cashbook/fund';
import { Modify } from '../common';
export type BranchSupplier = {
    _id?: string;
    storeId: string;
    name: string;
    phone?: string;
    /**
     * default ko có nhà cung cấp
     * ko thể thay đổi
     */
    immutable?: boolean;
};
export type Supplier = BranchSupplier & {
    branchId: string;
};
export type SupplierPayment = {
    purchaseId: string;
    paidAmount: number;
    total: number;
    isManualRecept: boolean;
    date: Date;
    paymentMethod: 'cash' | 'bank_transfer';
    supplier: Modify<BranchSupplier, {
        branchId: string;
    }>;
};
export type SupplierPaymentFund = Modify<FundRecord, {
    fundId: 'debt';
    group: 'SUPPLIER_PAYMENT_DEBT' | 'SUPPLIER_PAYMENT_PAYBACK';
    details: {
        paymentMethod: SupplierPayment['paymentMethod'];
        /**
         * phiếu tự động từ hóa đơn nhập hàng ko thể hủy tay
         * -> chỉ đc phép hủy hóa đơn thì tất cả phiếu liên quan sẽ hủy theo
         *
         * phiếu vào tay hủy
         * -> các hóa đơn nhập hàng cũng bị hủy theo
         */
        isManualRecept: boolean;
        supplier: SupplierPayment['supplier'];
        totalSupplierDebtAfterPayment: number;
        refPurchases: Array<{
            purchaseId: string;
            total: number;
            debt: number;
            paidAmount: number;
            status: 'completed' | 'cancelled';
        }>;
    };
}>;
