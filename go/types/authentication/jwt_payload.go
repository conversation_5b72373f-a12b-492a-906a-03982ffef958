// This file was generated from TypeScript interface using conversion tool, do not modify it directly.
// To parse and unparse this JSON data, add this code to your project and do:
//
//    userJwtPayload, err := UnmarshalUserJwtPayload(bytes)
//    bytes, err = userJwtPayload.Marshal()

package authentication

import "encoding/json"

func UnmarshalUserJwtPayload(data []byte) (UserJwtPayload, error) {
	var r UserJwtPayload
	err := json.Unmarshal(data, &r)
	return r, err
}

func (r *UserJwtPayload) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

type UserJwtPayload struct {
	ID         string  `json:"_id"`
	Type       string  `json:"type"`                 // Từ User['type'], là một trong 'system' | 'employee' | 'affiliate_partner'
	Status     string  `json:"status"`               // Từ User['status'], là một trong 'pending_verify' | 'active' | 'inactive' | 'deleted'
	EmployeeID *string `json:"employeeId,omitempty"` // Từ User['status']
}
