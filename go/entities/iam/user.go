// This file was generated from TypeScript interface using conversion tool, do not modify it directly.
// To parse and unparse this JSON data, add this code to your project and do:
//
//    user, err := UnmarshalUser(bytes)
//    bytes, err = user.Marshal()

package user

import "encoding/json"

func UnmarshalUser(data []byte) (User, error) {
	var r User
	err := json.Unmarshal(data, &r)
	return r, err
}

func (r *User) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

type User struct {
	ID          string `json:"_id"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	PhoneNumber string `json:"phoneNumber"`
	Password    string `json:"password"`
	// các loại có thể login vào hệ thống
	// system: Admin hệ thống
	// employee: Nhân viên 1 công ty
	// affiliate_partner: Đối tác affiliate vào xem kết quả doanh thu
	Type   string `json:"type"`   // 'system' | 'employee' | 'affiliate_partner'
	Status string `json:"status"` // 'pending_verify' | 'active' | 'inactive' | 'deleted'

	// một user có thể làm cho nhiều cửa hàng khác nhau
	// mỗi cửa hàng có 1 vị trí, permission khác nhau
	// hrm có thể tổng hợp bảng lương theo userId
	// kết nối thông qua HRM: Employee->employeeId
	// trong module HRM employeeId liên kết employee_branch
	EmployeeID *string `json:"employeeId,omitempty"`
}
