// This file was generated from JSON Schema using quicktype, do not modify it directly.
// To parse and unparse this JSON data, add this code to your project and do:
//
//    coordinates, err := UnmarshalCoordinates(bytes)
//    bytes, err = coordinates.Marshal()
//
//    distanceDetails, err := UnmarshalDistanceDetails(bytes)
//    bytes, err = distanceDetails.Marshal()
//
//    place, err := UnmarshalPlace(bytes)
//    bytes, err = place.Marshal()
//
//    placeSuggestions, err := UnmarshalPlaceSuggestions(bytes)
//    bytes, err = placeSuggestions.Marshal()
//
//    businessDetails, err := UnmarshalBusinessDetails(bytes)
//    bytes, err = businessDetails.Marshal()
//
//    company, err := UnmarshalCompany(bytes)
//    bytes, err = company.Marshal()

package Company

import (
	"encoding/json"
	"time"
	// "go.mongodb.org/mongo-driver/bson"
)

func UnmarshalCoordinates(data []byte) (Coordinates, error) {
	var r Coordinates
	err := json.Unmarshal(data, &r)
	return r, err
}

func (r *Coordinates) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

func UnmarshalDistanceDetails(data []byte) (DistanceDetails, error) {
	var r DistanceDetails
	err := json.Unmarshal(data, &r)
	return r, err
}

func (r *DistanceDetails) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

func UnmarshalPlace(data []byte) (Place, error) {
	var r Place
	err := json.Unmarshal(data, &r)
	return r, err
}

func (r *Place) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

type PlaceSuggestions []PlaceSuggestion

func UnmarshalPlaceSuggestions(data []byte) (PlaceSuggestions, error) {
	var r PlaceSuggestions
	err := json.Unmarshal(data, &r)
	return r, err
}

func (r *PlaceSuggestions) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

func UnmarshalBusinessDetails(data []byte) (BusinessDetails, error) {
	var r BusinessDetails
	err := json.Unmarshal(data, &r)
	return r, err
}

func (r *BusinessDetails) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

func UnmarshalCompany(data []byte) (Company, error) {
	var r Company
	err := json.Unmarshal(data, &r)
	return r, err
}

func (r *Company) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

type Coordinates struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type DistanceDetails struct {
	Distance float64 `json:"distance"`
	Source   string  `json:"source"`
}

type Place struct {
	City      *string                   `json:"city,omitempty"`
	Country   *string                   `json:"country,omitempty"`
	Distance  *PurpleDistance           `json:"distance,omitempty"`
	Distances map[string]FluffyDistance `json:"distances,omitempty"`
	// nhập theo chọn địa chỉ từ google api
	FullAddress          string         `json:"fullAddress"`
	HasReversedGeocoding *bool          `json:"hasReversedGeocoding,omitempty"`
	Instruction          *string        `json:"instruction,omitempty"`
	Instructions         []string       `json:"instructions,omitempty"`
	Lat                  *float64       `json:"lat,omitempty"`
	Lng                  *float64       `json:"lng,omitempty"`
	MainAddress          *string        `json:"mainAddress,omitempty"`
	PlaceID              *string        `json:"placeId,omitempty"`
	PlaceIDSource        *string        `json:"placeIdSource,omitempty"`
	PlusCode             *PlacePlusCode `json:"plus_code,omitempty"`
	PostalCode           *string        `json:"postalCode,omitempty"`
	Province             *string        `json:"province,omitempty"`
	SecondaryAddress     *string        `json:"secondaryAddress,omitempty"`
	Source               *string        `json:"source,omitempty"`
	// nhập từ form
	Street   *string        `json:"street,omitempty"`
	Viewport *PlaceViewport `json:"viewport,omitempty"`
}

type PurpleDistance struct {
	Distance float64 `json:"distance"`
	Source   string  `json:"source"`
}

type FluffyDistance struct {
	Distance float64 `json:"distance"`
	Source   string  `json:"source"`
}

type PlacePlusCode struct {
	CompoundCode string `json:"compound_code"`
	GlobalCode   string `json:"global_code"`
}

type PlaceViewport struct {
	Northeast PurpleNortheast `json:"northeast"`
	Southwest PurpleSouthwest `json:"southwest"`
}

type PurpleNortheast struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type PurpleSouthwest struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type PlaceSuggestion struct {
	City      *string                   `json:"city,omitempty"`
	Country   *string                   `json:"country,omitempty"`
	Distance  *TentacledDistance        `json:"distance,omitempty"`
	Distances map[string]StickyDistance `json:"distances,omitempty"`
	// nhập theo chọn địa chỉ từ google api
	FullAddress          string                   `json:"fullAddress"`
	HasReversedGeocoding *bool                    `json:"hasReversedGeocoding,omitempty"`
	Instruction          *string                  `json:"instruction,omitempty"`
	Instructions         []string                 `json:"instructions,omitempty"`
	Lat                  *float64                 `json:"lat,omitempty"`
	Lng                  *float64                 `json:"lng,omitempty"`
	MainAddress          *string                  `json:"mainAddress,omitempty"`
	PlaceID              *string                  `json:"placeId,omitempty"`
	PlaceIDSource        *string                  `json:"placeIdSource,omitempty"`
	PlusCode             *PlaceSuggestionPlusCode `json:"plus_code,omitempty"`
	PostalCode           *string                  `json:"postalCode,omitempty"`
	Province             *string                  `json:"province,omitempty"`
	SecondaryAddress     *string                  `json:"secondaryAddress,omitempty"`
	Source               *string                  `json:"source,omitempty"`
	// nhập từ form
	Street   *string                  `json:"street,omitempty"`
	Viewport *PlaceSuggestionViewport `json:"viewport,omitempty"`
}

type TentacledDistance struct {
	Distance float64 `json:"distance"`
	Source   string  `json:"source"`
}

type StickyDistance struct {
	Distance float64 `json:"distance"`
	Source   string  `json:"source"`
}

type PlaceSuggestionPlusCode struct {
	CompoundCode string `json:"compound_code"`
	GlobalCode   string `json:"global_code"`
}

type PlaceSuggestionViewport struct {
	Northeast FluffyNortheast `json:"northeast"`
	Southwest FluffySouthwest `json:"southwest"`
}

type FluffyNortheast struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type FluffySouthwest struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type BusinessDetails struct {
	Address           BusinessDetailsAddress     `json:"address"`
	BusinessName      *string                    `json:"businessName,omitempty"`
	Description       *string                    `json:"description,omitempty"`
	EstablishmentDate *time.Time                 `json:"establishmentDate,omitempty"`
	Industries        []string                   `json:"industries,omitempty"`
	TaxAuthority      *string                    `json:"taxAuthority,omitempty"`
	TaxCode           *string                    `json:"taxCode,omitempty"`
	WorkEmail         *string                    `json:"workEmail,omitempty"`
	WorkPhones        []BusinessDetailsWorkPhone `json:"workPhones"`
}

type BusinessDetailsAddress struct {
	City      *string                     `json:"city,omitempty"`
	Country   *string                     `json:"country,omitempty"`
	Distance  *IndigoDistance             `json:"distance,omitempty"`
	Distances map[string]IndecentDistance `json:"distances,omitempty"`
	// nhập theo chọn địa chỉ từ google api
	FullAddress          string          `json:"fullAddress"`
	HasReversedGeocoding *bool           `json:"hasReversedGeocoding,omitempty"`
	Instruction          *string         `json:"instruction,omitempty"`
	Instructions         []string        `json:"instructions,omitempty"`
	Lat                  *float64        `json:"lat,omitempty"`
	Lng                  *float64        `json:"lng,omitempty"`
	MainAddress          *string         `json:"mainAddress,omitempty"`
	PlaceID              *string         `json:"placeId,omitempty"`
	PlaceIDSource        *string         `json:"placeIdSource,omitempty"`
	PlusCode             *PurplePlusCode `json:"plus_code,omitempty"`
	PostalCode           *string         `json:"postalCode,omitempty"`
	Province             *string         `json:"province,omitempty"`
	SecondaryAddress     *string         `json:"secondaryAddress,omitempty"`
	Source               *string         `json:"source,omitempty"`
	// nhập từ form
	Street   *string         `json:"street,omitempty"`
	Viewport *PurpleViewport `json:"viewport,omitempty"`
}

type IndigoDistance struct {
	Distance float64 `json:"distance"`
	Source   string  `json:"source"`
}

type IndecentDistance struct {
	Distance float64 `json:"distance"`
	Source   string  `json:"source"`
}

type PurplePlusCode struct {
	CompoundCode string `json:"compound_code"`
	GlobalCode   string `json:"global_code"`
}

type PurpleViewport struct {
	Northeast TentacledNortheast `json:"northeast"`
	Southwest TentacledSouthwest `json:"southwest"`
}

type TentacledNortheast struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type TentacledSouthwest struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type BusinessDetailsWorkPhone struct {
	PhoneNumber string `json:"phoneNumber"`
}

// Company Entity để dễ dàng mở rộng thành ERP sau này
type Company struct {
	ID                string             `bson:"_id,omitempty"`
	Address           CompanyAddress     `json:"address"`
	BusinessName      *string            `json:"businessName,omitempty"`
	Description       *string            `json:"description,omitempty"`
	EstablishmentDate *time.Time         `json:"establishmentDate,omitempty"`
	Industries        []string           `json:"industries,omitempty"`
	Owner             Owner              `json:"owner"`
	TaxAuthority      *string            `json:"taxAuthority,omitempty"`
	TaxCode           *string            `json:"taxCode,omitempty"`
	WorkEmail         *string            `json:"workEmail,omitempty"`
	WorkPhones        []CompanyWorkPhone `json:"workPhones"`
}

type CompanyAddress struct {
	City      *string                      `json:"city,omitempty"`
	Country   *string                      `json:"country,omitempty"`
	Distance  *HilariousDistance           `json:"distance,omitempty"`
	Distances map[string]AmbitiousDistance `json:"distances,omitempty"`
	// nhập theo chọn địa chỉ từ google api
	FullAddress          string          `json:"fullAddress"`
	HasReversedGeocoding *bool           `json:"hasReversedGeocoding,omitempty"`
	Instruction          *string         `json:"instruction,omitempty"`
	Instructions         []string        `json:"instructions,omitempty"`
	Lat                  *float64        `json:"lat,omitempty"`
	Lng                  *float64        `json:"lng,omitempty"`
	MainAddress          *string         `json:"mainAddress,omitempty"`
	PlaceID              *string         `json:"placeId,omitempty"`
	PlaceIDSource        *string         `json:"placeIdSource,omitempty"`
	PlusCode             *FluffyPlusCode `json:"plus_code,omitempty"`
	PostalCode           *string         `json:"postalCode,omitempty"`
	Province             *string         `json:"province,omitempty"`
	SecondaryAddress     *string         `json:"secondaryAddress,omitempty"`
	Source               *string         `json:"source,omitempty"`
	// nhập từ form
	Street   *string         `json:"street,omitempty"`
	Viewport *FluffyViewport `json:"viewport,omitempty"`
}

type HilariousDistance struct {
	Distance float64 `json:"distance"`
	Source   string  `json:"source"`
}

type AmbitiousDistance struct {
	Distance float64 `json:"distance"`
	Source   string  `json:"source"`
}

type FluffyPlusCode struct {
	CompoundCode string `json:"compound_code"`
	GlobalCode   string `json:"global_code"`
}

type FluffyViewport struct {
	Northeast StickyNortheast `json:"northeast"`
	Southwest StickySouthwest `json:"southwest"`
}

type StickyNortheast struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type StickySouthwest struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type Owner struct {
	ID   string `json:"_id"`
	Name string `json:"name"`
}

type CompanyWorkPhone struct {
	PhoneNumber string `json:"phoneNumber"`
}
