# Ca Làm Việc Nhân Viên

## <PERSON><PERSON> tả
Hệ thống quản lý ca làm việc cho phép lên lịch và theo dõi thời gian làm việc của nhân viên. Lịch làm việc được tạo trước một khoảng thời gian cố định (1 tuần, 2 tuần hoặc 1 tháng) tùy theo chính sách công ty. Hệ thống tự động tạo ca làm việc dựa trên lịch biểu đã được cài đặt, có tính đến ngày nghỉ lễ và các yêu cầu nghỉ phép của nhân viên. Nhân viên có thể xem lịch làm việc trước và yêu cầu đổi ca nếu cần thiết.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_shift.interface.ts` (`IEmployeeShift`)
- **Fields**:
  - `_id`: string - ID duy nhất của ca làm việc
  - `employee`: object - Thông tin nhân viên
    - `_id`: string - ID nhân viên
    - `userId`: string - ID người dùng
    - `name`: string - Tên nhân viên
  - `companyId`: string - ID công ty
  - `storeId`: string - ID cửa hàng
  - `branchId`: string - ID chi nhánh
  - `status`: string - Trạng thái ca làm việc
    - `scheduled`: Đã lên lịch
    - `approved`: Đã duyệt, chưa check-in
    - `checked_in`: Đã check-in, đang làm việc
    - `completed`: Đã check-out, hoàn thành ca
    - `absent`: Vắng mặt không phép
    - `canceled`: Đã hủy (có báo trước)
  - `swapRequest`: Array - Danh sách yêu cầu đổi ca
    - `requestedBy`: object - Người yêu cầu đổi ca
      - `_id`: string - ID người dùng
      - `employeeId`: string - ID nhân viên
      - `name`: string - Tên nhân viên
    - `reviewedBy`: object - Người duyệt yêu cầu
      - `_id`: string - ID người dùng
      - `employeeId`: string - ID nhân viên
      - `name`: string - Tên nhân viên
    - `status`: "pending" | "approved" | "rejected" - Trạng thái yêu cầu
  - `checkIn`: string - Giờ vào ca (định dạng "HH:mm")
  - `checkOut`: string - Giờ ra ca (định dạng "HH:mm")
  - `workedHours`: number - Số giờ đã làm việc
  - `lateMinutes`: number - Số phút đi muộn
  - `earlyLeaveMinutes`: number - Số phút về sớm
  - `extraHours`: number - Số giờ làm thêm
  - `overtime`: boolean - Có tính làm thêm giờ không
  - `date`: string - Ngày làm việc
  - `time`: Time - Thời gian (dùng cho báo cáo)
  - `createdAt`: Date - Thời điểm tạo bản ghi
  - `updatedAt`: Date - Thời điểm cập nhật cuối cùng

## Ví dụ
```json
{
  "_id": "SHIFT001",
  "employee": {
    "_id": "NV001",
    "userId": "USER001",
    "name": "Nguyễn Văn A"
  },
  "companyId": "CTY001",
  "storeId": "CH001",
  "branchId": "CN001",
  "status": "completed",
  "swapRequest": [
    {
      "requestedBy": {
        "_id": "USER002",
        "employeeId": "NV002",
        "name": "Trần Thị B"
      },
      "reviewedBy": {
        "_id": "MGR001",
        "employeeId": "QL001",
        "name": "Lê Văn C"
      },
      "status": "approved"
    }
  ],
  "checkIn": "08:00",
  "checkOut": "17:30",
  "workedHours": 8.5,
  "lateMinutes": 0,
  "earlyLeaveMinutes": 0,
  "extraHours": 1.5,
  "overtime": true,
  "date": "2024-02-24",
  "time": {
    "date": 24,
    "month": 2,
    "year": 2024,
    "hours": 17,
    "minutes": 30
  },
  "createdAt": "2024-02-23T17:00:00.000Z",
  "updatedAt": "2024-02-24T17:30:00.000Z"
}
```

## Lưu ý
- Ca làm việc được tạo tự động dựa trên lịch biểu cố định (`employee_shift_schedules`)
- Hệ thống kiểm tra ngày nghỉ lễ và đơn xin nghỉ phép trước khi tạo ca
- Thời gian làm việc (`checkIn`, `checkOut`) được lưu theo định dạng 24 giờ "HH:mm"
- Các đơn vị thời gian:
  - `workedHours` và `extraHours`: tính bằng giờ
  - `lateMinutes` và `earlyLeaveMinutes`: tính bằng phút
- Khi có yêu cầu đổi ca được duyệt, thông tin `employee` sẽ được cập nhật thành nhân viên mới
- Trạng thái `status` thay đổi theo luồng: scheduled -> approved -> checked_in -> completed (hoặc absent/canceled) 