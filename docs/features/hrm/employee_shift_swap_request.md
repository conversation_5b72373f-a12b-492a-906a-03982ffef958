# Yêu Cầu Đổi Ca Làm Việc

## <PERSON><PERSON> tả
Hệ thống quản lý yêu cầu đổi ca cho phép nhân viên gửi đề xuất đổi ca với nhân viên khác. <PERSON>hi được quản lý phê duyệt, hệ thống sẽ tự động cập nhật thông tin ca làm việc giữa hai nhân viên và ghi lại log giao dịch đổi ca.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_shift_swap_request.interface.ts` (`IEmployeeShiftSwapRequest`)
- **Fields**:
  - `_id`: string - ID duy nhất của yêu cầu đổi ca
  - `employee`: object - Thông tin nhân viên
    - `_id`: string - ID nhân viên
    - `userId`: string - ID người dùng
    - `name`: string - Tên nhân viên
  - `companyId`: string - ID công ty
  - `storeId`: string - ID cửa hàng
  - `branchId`: string - ID chi nhánh
  - `shiftId`: string - ID ca làm việc
  - `date`: string - Ngày xin đổi ca
  - `requestedBy`: object - Người yêu cầu đổi ca
    - `employeeId`: string - ID nhân viên yêu cầu
    - `name`: string - Tên nhân viên yêu cầu
  - `requestedTo`: object - Người được yêu cầu đổi ca
    - `employeeId`: string - ID nhân viên được yêu cầu
    - `name`: string - Tên nhân viên được yêu cầu
  - `reviewedBy`: object - Người duyệt yêu cầu
    - `employeeId`: string - ID người duyệt
    - `name`: string - Tên người duyệt
  - `status`: "pending" | "approved" | "rejected" - Trạng thái yêu cầu
  - `createdAt`: string - Thời điểm tạo yêu cầu
  - `updatedAt`: string - Thời điểm cập nhật cuối cùng

## Quy Trình Xử Lý
1. Nhân viên A gửi yêu cầu đổi ca với nhân viên B
2. Nếu được duyệt → Hệ thống tự động:
   - Cập nhật `userShift.userId = requestedTo._id`
   - Ghi log giao dịch đổi ca
3. Nếu bị từ chối → Không thay đổi ca làm việc

## Ví dụ
```json
{
  "_id": "SR001",
  "employee": {
    "_id": "NV001",
    "userId": "USER001",
    "name": "Nguyễn Văn A"
  },
  "companyId": "CTY001",
  "storeId": "CH001",
  "branchId": "CN001",
  "shiftId": "CA001",
  "date": "2024-02-24",
  "requestedBy": {
    "employeeId": "NV001",
    "name": "Nguyễn Văn A"
  },
  "requestedTo": {
    "employeeId": "NV002",
    "name": "Trần Văn B"
  },
  "reviewedBy": {
    "employeeId": "QL001",
    "name": "Lê Thị C"
  },
  "status": "approved",
  "createdAt": "2024-02-23T10:00:00.000Z",
  "updatedAt": "2024-02-23T11:00:00.000Z"
}
``` 