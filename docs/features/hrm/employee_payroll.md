# Lương & Thưởng/Phạt Nhân Viên

## <PERSON><PERSON> tả
Hệ thống tự động tính lươ<PERSON>, thưởng KPI và phạt đi trễ/về sớm cho nhân viên. Tích hợp vào bảng bá<PERSON> c<PERSON><PERSON> lương theo từng nhân viên, bao gồm lư<PERSON> c<PERSON> bản, gi<PERSON> làm thêm, ca đêm, thưởng, phạt và tổng lương cuối cùng.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_payroll.interface.ts` (`IEmployeePayroll`)
- **Fields**:
  - `id`: number - ID duy nhất của bảng lương
  - `employee`: object - Thông tin nhân viên
    - `_id`: string - ID nhân viên
    - `userId`: string - ID người dùng
    - `name`: string - Tên nhân viên
  - `companyId`: string - ID công ty
  - `storeId`: string - ID cửa hàng
  - `branchId`: string - ID chi nhánh
  - `baseSalary`: number - L<PERSON><PERSON><PERSON> c<PERSON> bản
  - `workingHours`: number - Tổng số giờ làm
  - `overtimeHours`: number - Số giờ làm thêm
  - `totalNightShiftHours`: number - Tổng số giờ làm ca đêm
  - `totalBonuses`: number - Tổng tiền thưởng
  - `totalPenalties`: number - Tổng tiền phạt
  - `finalSalary`: number - Tổng lương sau khi tính hết thưởng/phạt
  - `dailyRecords`: Array - Chi tiết chấm công theo ngày
    - `date`: string - Ngày làm việc
    - `shift`: object - Thông tin ca làm
      - `_id`: string - ID ca làm
      - `shiftId`: string - ID tham chiếu ca làm
    - `checkIn`: string - Giờ vào ca
    - `checkOut`: string - Giờ ra ca
    - `workedHours`: number - Số giờ làm việc thực tế
    - `overtimeHours`: number - Số giờ làm thêm
    - `nightShiftHours`: number - Số giờ làm ca đêm
    - `bonus`: number - Tiền thưởng trong ngày
    - `penalty`: number - Tiền phạt trong ngày
    - `note`: string - Ghi chú
  - `bonuses`: Array - Danh sách thưởng
    - `date`: string - Ngày thưởng
    - `type`: IEmployeeBonus - Loại thưởng
    - `amount`: number - Số tiền thưởng
    - `reason`: string - Lý do thưởng
    - `relatedKpiRuleId`: string - ID quy tắc KPI liên quan
  - `penalties`: Array - Danh sách phạt
    - `date`: string - Ngày phạt
    - `type`: IEmployeePenalty - Loại phạt
    - `amount`: number - Số tiền phạt
    - `reason`: string - Lý do phạt
  - `note`: string - Ghi chú chung
  - `month`: string - Tháng tính lương
  - `time`: Time - Thời gian (dùng cho báo cáo)
  - `createdAt`: Date - Thời điểm tạo bảng lương
  - `updatedAt`: Date - Thời điểm cập nhật cuối cùng

## Ví dụ
```json
{
  "id": 1,
  "employee": {
    "_id": "NV001",
    "userId": "USER001",
    "name": "Nguyễn Văn A"
  },
  "companyId": "CTY001",
  "storeId": "CH001",
  "branchId": "CN001",
  "baseSalary": 5000000,
  "workingHours": 176,
  "overtimeHours": 8,
  "totalNightShiftHours": 16,
  "totalBonuses": 1000000,
  "totalPenalties": 200000,
  "finalSalary": 5800000,
  "dailyRecords": [
    {
      "date": "2024-02-24",
      "shift": {
        "_id": "CA001",
        "shiftId": "SHIFT001"
      },
      "checkIn": "08:00",
      "checkOut": "17:00",
      "workedHours": 8,
      "overtimeHours": 1,
      "nightShiftHours": 0,
      "bonus": 100000,
      "penalty": 0,
      "note": "Làm thêm 1 giờ"
    }
  ],
  "bonuses": [
    {
      "date": "2024-02-24",
      "type": "KPI_BONUS",
      "amount": 500000,
      "reason": "Đạt KPI tháng",
      "relatedKpiRuleId": "KPI001"
    }
  ],
  "penalties": [
    {
      "date": "2024-02-25",
      "type": "LATE",
      "amount": 50000,
      "reason": "Đi trễ 15 phút"
    }
  ],
  "note": "Tháng làm việc hiệu quả",
  "month": "2024-02",
  "time": {
    "date": 1,
    "month": 2,
    "year": 2024,
    "hours": 0,
    "minutes": 0
  },
  "createdAt": "2024-02-01T00:00:00.000Z",
  "updatedAt": "2024-02-29T23:59:59.000Z"
}
``` 