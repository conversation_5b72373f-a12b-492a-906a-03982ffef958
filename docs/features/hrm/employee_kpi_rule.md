# Quy Tắc KPI Nhân Viên

## <PERSON><PERSON> tả
Hệ thống quản lý quy tắc KPI cho phép thiết lập các tiêu chí đánh giá hiệu suất công việc cho nhân viên hoặc nhóm nhân viên. Khi nhân viên đạt được các tiêu chí đề ra, hệ thống sẽ tự động tạo khoản thưởng trong bảng lương. Quy tắc có thể được áp dụng linh hoạt cho từng cá nhân hoặc theo nhóm, với các hình thức khen thưởng đa dạng.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_kpi_rule.interface.ts` (`IEmployeeKpiRule`)
- **Fields**:
  - `_id`: string - ID duy nhất của quy tắc KPI
  - `companyId`: string - ID công ty
  - `storeId`: string - ID cửa hàng
  - `branchId`: string - ID chi nhánh
  - `name`: string - Tên quy tắc <PERSON>PI
  - `description`: string - Mô tả chi tiết quy tắc
  - `applyTo`: "group" | "employee" - Đối tượng áp dụng
    - `group`: Áp dụng cho nhóm
    - `employee`: Áp dụng cho cá nhân
  - `applyToIds`: string[] - Danh sách ID nhóm hoặc nhân viên được áp dụng
  - `criteria`: Array - Danh sách các tiêu chí đánh giá
    - `metric`: keyof IEmployeeKpi['metrics'] - Chỉ số KPI cần đạt
    - `condition`: ">=" | "<=" | "==" | "!=" - Điều kiện so sánh
    - `value`: number - Giá trị cần đạt
  - `reward`: object - Thông tin phần thưởng
    - `amount`: number - Giá trị phần thưởng (VD: 500000)
    - `type`: "cash" | "voucher" | "bonus_point" - Loại phần thưởng
      - `cash`: Tiền mặt
      - `voucher`: Phiếu quà tặng
      - `bonus_point`: Điểm thưởng
  - `approvalRequired`: boolean - Yêu cầu phê duyệt thưởng
  - `active`: boolean - Trạng thái kích hoạt của quy tắc
  - `createdAt`: Date - Thời điểm tạo bản ghi
  - `updatedAt`: Date - Thời điểm cập nhật cuối cùng

## Ví dụ
```json
{
  "_id": "KPI001",
  "companyId": "CTY001",
  "storeId": "CH001",
  "branchId": "CN001",
  "name": "Thưởng doanh số quý 1/2024",
  "description": "Thưởng cho nhân viên sale đạt doanh số trên 500 triệu trong quý 1/2024",
  "applyTo": "group",
  "applyToIds": ["SALE_TEAM_01", "SALE_TEAM_02"],
  "criteria": [
    {
      "metric": "quarterly_sales",
      "condition": ">=",
      "value": *********
    },
    {
      "metric": "customer_satisfaction",
      "condition": ">=",
      "value": 4.5
    }
  ],
  "reward": {
    "amount": 5000000,
    "type": "cash"
  },
  "approvalRequired": true,
  "active": true,
  "createdAt": "2024-02-24T08:00:00.000Z",
  "updatedAt": "2024-02-24T17:00:00.000Z"
}
```

## Lưu ý
- Các `metric` trong `criteria` phải tương ứng với các chỉ số được định nghĩa trong interface `IEmployeeKpi['metrics']`
- Khi `approvalRequired` là `true`, khoản thưởng sẽ cần được quản lý phê duyệt trước khi được thêm vào bảng lương
- Một nhân viên có thể được áp dụng nhiều quy tắc KPI cùng lúc
- Chỉ những quy tắc có `active: true` mới được hệ thống xét thưởng
- Giá trị `amount` trong phần thưởng nên được định dạng theo VND (VD: 5000000 = 5 triệu đồng) 