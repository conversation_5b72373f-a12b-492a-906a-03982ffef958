# Chấm Công Nhân Viên

## <PERSON><PERSON> tả
Hệ thống chấm công cho phép theo dõi thời gian làm việc của nhân viên thông qua nhiều phương thức khác nhau như quét mã QR, GPS, máy chấm công vật lý, v.v. <PERSON><PERSON> thống cũng hỗ trợ việc điều chỉnh giờ công khi có sự cố, với quy trình phê duyệt minh bạch và đầy đủ bằng chứng.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_attendance.interface.ts` (`IEmployeeAttendance`)
- **Fields**:
  - `id`: number - ID duy nhất của bản ghi chấm công
  - `employee`: object - Thông tin nhân viên
    - `_id`: string - ID nhân viên
    - `userId`: string - ID người dùng
    - `name`: string - Tên nhân viên
  - `companyId`: string - ID công ty
  - `storeId`: string - ID cửa hàng
  - `branchId`: string - ID chi nhánh
  - `clockIn`: Date - Thời điểm chấm công vào
  - `clockOut`: Date - Thời điểm chấm công ra
  - `checkInSource`: IAttendanceCheckSource - Nguồn chấm công vào
  - `checkOutSource`: IAttendanceCheckSource - Nguồn chấm công ra
  - `checkInDevice`: string - Thiết bị chấm công vào
  - `checkOutDevice`: string - Thiết bị chấm công ra
  - `checkInLocation`: string - Vị trí chấm công vào
  - `checkOutLocation`: string - Vị trí chấm công ra
  - `adjustments`: Array - Danh sách điều chỉnh giờ công
    - `type`: "CHECKIN" | "CHECKOUT" - Loại điều chỉnh
    - `oldTime`: Date - Thời gian cũ
    - `newTime`: Date - Thời gian mới
    - `requestedBy`: object - Người yêu cầu điều chỉnh
      - `_id`: string - ID người yêu cầu
      - `name`: string - Tên người yêu cầu
    - `approvedBy`: object - Người phê duyệt
      - `_id`: string - ID người phê duyệt
      - `name`: string - Tên người phê duyệt
    - `approvedAt`: Date - Thời điểm phê duyệt
    - `reason`: string - Lý do điều chỉnh
    - `evidenceImages`: Array - Hình ảnh minh chứng
      - `url`: string - Đường dẫn hình ảnh
  - `status`: "pending" | "approved" | "rejected" | "normal" - Trạng thái chấm công
  - `date`: string - Ngày chấm công
  - `time`: Time - Thời gian (dùng cho báo cáo)
  - `createdAt`: Date - Thời điểm tạo bản ghi
  - `updatedAt`: Date - Thời điểm cập nhật cuối cùng

## Nguồn Chấm Công (IAttendanceCheckSource)
- `qr_code` - Quét mã QR
- `gps` - Định vị GPS
- `pos` - Hệ thống POS
- `time_attendance_machine` - Máy chấm công vật lý
- `manual` - Nhập tay

## Ví dụ
```json
{
  "id": 1,
  "employee": {
    "_id": "NV001",
    "userId": "USER001",
    "name": "Nguyễn Văn A"
  },
  "companyId": "CTY001",
  "storeId": "CH001",
  "branchId": "CN001",
  "clockIn": "2024-02-24T08:00:00.000Z",
  "clockOut": "2024-02-24T17:00:00.000Z",
  "checkInSource": "qr_code",
  "checkOutSource": "qr_code",
  "checkInDevice": "iPhone 12",
  "checkOutDevice": "iPhone 12",
  "checkInLocation": "Văn phòng Hà Nội",
  "checkOutLocation": "Văn phòng Hà Nội",
  "adjustments": [
    {
      "type": "CHECKIN",
      "oldTime": "2024-02-24T08:30:00.000Z",
      "newTime": "2024-02-24T08:00:00.000Z",
      "requestedBy": {
        "_id": "NV001",
        "name": "Nguyễn Văn A"
      },
      "approvedBy": {
        "_id": "QL001",
        "name": "Trần Thị B"
      },
      "approvedAt": "2024-02-24T10:00:00.000Z",
      "reason": "Lỗi hệ thống khi chấm công",
      "evidenceImages": [
        {
          "url": "https://example.com/evidence1.jpg"
        }
      ]
    }
  ],
  "status": "approved",
  "date": "2024-02-24",
  "time": {
    "date": 9,
    "month": 9,
    "year": 9,
    "hours": 9,
    "minutes": 0
  },
  "createdAt": "2024-02-24T08:00:00.000Z",
  "updatedAt": "2024-02-24T10:00:00.000Z"
}
