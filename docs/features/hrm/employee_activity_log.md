# Employee Activity Log

## Description
Thao tác của tất cả các tài khoản trên trang quản trị đều được ghi nhận lại, nhằm hỗ trợ chủ shop theo sát hoạt động của nhân viên. Tính năng này lưu lại các hành động quan trọng để theo dõi, phân tích và đảm bảo tính minh bạch.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_activity_log.ts` (`IEmployeeActivityLog`)
- **Fields**:
  - `_id`: string - ID duy nhất của log
  - `userId`: string - ID user thực hiện hành động
  - `staffId`: string - ID nhân viên thực hiện hành động
  - `staffName`: string - Tên nhân viên (nhúng để dễ truy vấn)
  - `role`: string - <PERSON><PERSON> trò của nhân viên (nhúng để dễ truy vấn)
  - `action`: "create" | "update" | "delete" | "show" | "hide" | "import" | "select" - Loại thao tác
  - `entityType`: "product" | "order" | "customer" - Loại đối tượng bị ảnh hưởng
  - `entityId`: string - ID của đối tượng bị ảnh hưởng
  - `entityName`: string - Tên đối tượng (nhúng để hiển thị trực quan)
  - `details`: object - Chi tiết bổ sung về hành động:
    - `fieldChanged`: string - Trường bị thay đổi (nếu có)
    - `oldValue`: string - Giá trị cũ (nếu có)
    - `newValue`: string - Giá trị mới (nếu có)
  - `timestamp`: string - Thời gian thực hiện (ISO date string)
  - `ipAddress`: string - Địa chỉ IP của nhân viên (tùy chọn, tăng tính bảo mật)

## Example
```json
{
  "_id": "LOG123",
  "userId": "User001",
  "staffId": "STAFF001",
  "staffName": "Nguyễn Văn A",
  "role": "manager",
  "action": "update",
  "entityType": "product",
  "entityId": "PROD001",
  "entityName": "iPhone 14",
  "details": {
    "fieldChanged": "price",
    "oldValue": "20000000",
    "newValue": "21000000"
  },
  "timestamp": "2025-02-23T10:00:00Z",
  "ipAddress": "*************"
}