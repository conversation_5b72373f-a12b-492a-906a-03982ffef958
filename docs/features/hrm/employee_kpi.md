# Chỉ Số KPI Nhân Viên

## <PERSON><PERSON> tả
Hệ thống theo dõi và đánh giá hiệu suất làm việc của nhân viên thông qua các chỉ số KPI (Key Performance Indicators). Các chỉ số này đư<PERSON>c thu thập và tính to<PERSON>, bao gồ<PERSON> do<PERSON> thu, số lượng đơn hàng, kh<PERSON><PERSON> hàng mới, tỷ lệ chuyển đổi và các chỉ số chất lượng dịch vụ khác.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_kpi.interface.ts` (`IEmployeeKpi`)
- **Fields**:
  - `_id`: string - ID duy nhất của bản ghi KPI
  - `userId`: string - ID người dùng
  - `companyId`: string - ID công ty
  - `storeId`: string - ID cửa hàng
  - `branchId`: string - ID chi nhánh
  - `groups`: string[] - <PERSON><PERSON> s<PERSON><PERSON> các nhóm nhân viên thuộc về
  - `employee`: object - Thông tin nhân viên
    - `_id`: string - ID nhân viên
    - `userId`: string - ID người dùng
    - `name`: string - Tên nhân viên
  - `month`: string - Tháng đánh giá (định dạng: "YYYY-MM")
  - `metrics`: object - Các chỉ số KPI
    - `totalRevenue`: number - Tổng doanh thu (VND)
    - `orderCount`: number - Số lượng đơn hàng
    - `newCustomers`: number - Số lượng khách hàng mới
    - `conversionRate`: number - Tỷ lệ chuyển đổi (%)
    - `avgOrderValue`: number - Giá trị đơn hàng trung bình (VND)
    - `serviceTime`: number - Thời gian phục vụ trung bình (phút)
    - `returnRate`: number - Tỷ lệ hoàn đơn (%)
  - `createdAt`: Date - Thời điểm tạo bản ghi
  - `updatedAt`: Date - Thời điểm cập nhật cuối cùng

## Ví dụ
```json
{
  "_id": "KPI_REC001",
  "userId": "USER001",
  "companyId": "CTY001",
  "storeId": "CH001",
  "branchId": "CN001",
  "groups": ["SALE_TEAM_01", "VIP_SALES"],
  "employee": {
    "_id": "NV001",
    "userId": "USER001",
    "name": "Nguyễn Văn A"
  },
  "month": "2024-02",
  "metrics": {
    "totalRevenue": *********,
    "orderCount": 45,
    "newCustomers": 15,
    "conversionRate": 35.5,
    "avgOrderValue": 11555555,
    "serviceTime": 25,
    "returnRate": 2.5
  },
  "createdAt": "2024-02-24T08:00:00.000Z",
  "updatedAt": "2024-02-24T17:00:00.000Z"
}
```

## Lưu ý
- Tất cả các giá trị tiền tệ (`totalRevenue`, `avgOrderValue`) được tính bằng VND
- `conversionRate` và `returnRate` được tính bằng phần trăm (%)
- `serviceTime` được tính bằng phút
- Định dạng `month` phải theo chuẩn "YYYY-MM" (VD: "2024-02" cho tháng 2 năm 2024)
- Một nhân viên có thể thuộc nhiều nhóm (`groups`) khác nhau
- Các chỉ số trong `metrics` được sử dụng để đánh giá KPI và tính thưởng theo các quy tắc trong `IEmployeeKpiRule` 