# Yêu Cầu Nghỉ Phép Nhân Viên

## <PERSON><PERSON> tả
Hệ thống quản lý yêu cầu nghỉ phép cho phép nhân viên gửi đơn xin nghỉ phép và quản lý có thể phê duyệt hoặc từ chối. Khi yêu cầu được phê duyệt, hệ thống sẽ tự động cập nhật trạng thái ca làm việc của nhân viên sang "đã hủy".

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_leave_request.interface.ts` (`IEmployeeLeaveRequest`)
- **Fields**:
  - `_id`: string - ID duy nhất của yêu cầu nghỉ phép
  - `employee`: object - Thông tin nhân viên
    - `_id`: string - ID nhân viên
    - `userId`: string - ID người dùng
    - `name`: string - Tên nhân viên
  - `companyId`: string - ID công ty
  - `storeId`: string - ID cửa hàng
  - `branchId`: string - ID chi nhánh
  - `shiftId`: string - ID ca làm việc
  - `date`: string - Ngày xin nghỉ
  - `reason`: string - Lý do xin nghỉ
  - `requestedBy`: object - Người yêu cầu
    - `_id`: string - ID người yêu cầu
    - `name`: string - Tên người yêu cầu
  - `reviewedBy`: object - Người duyệt
    - `_id`: string - ID người duyệt
    - `name`: string - Tên người duyệt
  - `status`: "pending" | "approved" | "rejected" - Trạng thái yêu cầu
  - `createdAt`: Date - Thời điểm tạo yêu cầu
  - `updatedAt`: Date - Thời điểm cập nhật cuối cùng
  - `time`: Time - Thời gian (dùng cho báo cáo)

## Quy Trình Xử Lý
1. Nhân viên gửi yêu cầu nghỉ phép
2. Nếu được duyệt → Hệ thống tự động cập nhật `employee_shifts.status = "canceled"`
3. Nếu bị từ chối → Không thay đổi ca làm việc, nhân viên vẫn phải đi làm

## Ví dụ
```json
{
  "_id": "LR001",
  "employee": {
    "_id": "NV001",
    "userId": "USER001",
    "name": "Nguyễn Văn A"
  },
  "companyId": "CTY001",
  "storeId": "CH001",
  "branchId": "CN001",
  "shiftId": "CA001",
  "date": "2024-02-24",
  "reason": "Có việc gia đình đột xuất",
  "requestedBy": {
    "_id": "NV001",
    "name": "Nguyễn Văn A"
  },
  "reviewedBy": {
    "_id": "QL001",
    "name": "Trần Thị B"
  },
  "status": "approved",
  "time": {
    "date": 24,
    "month": 2,
    "year": 2024,
    "hours": 8,
    "minutes": 0
  },
  "createdAt": "2024-02-23T15:00:00.000Z",
  "updatedAt": "2024-02-23T16:00:00.000Z"
}
``` 