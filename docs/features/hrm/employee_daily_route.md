# Giám Sát Lộ Trình Nhân Viên 

## <PERSON><PERSON> tả
Hệ thống giám sát lộ trình di chuyển của nhân viên sale thông qua GPS để đảm bảo hiệu quả công việc. Dữ liệu được thu thập qua mobile app và hiển thị trên bản đồ (Google Maps/Mapbox). Hệ thống có khả năng phát hiện các bất thường như không di chuyển hoặc ra khỏi khu vực làm việc, từ đó gửi cảnh báo cho quản lý khi phát hiện dấu hiệu gian lận.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_daily_route.interface.ts` (`IEmployeeDailyRoute`)
- **Fields**:
  - `_id`: string - ID duy nhất của bản ghi lộ trình
  - `employee`: object - Thông tin nhân viên
    - `_id`: string - ID nhân viên
    - `userId`: string - ID người dùng
    - `name`: string - Tên nhân viên
  - `companyId`: string - ID công ty
  - `storeId`: string - ID cửa hàng
  - `branchId`: string - ID chi nhánh
  - `date`: string - Ngày làm việc
  - `totalDistance`: number - Tổng quãng đường di chuyển (đơn vị: mét)
  - `checkpoints`: Array - Danh sách các điểm check-in
    - `time`: string - Thời điểm check-in
    - `location`: object - Tọa độ điểm check-in
      - `lat`: number - Vĩ độ
      - `lng`: number - Kinh độ
  - `status`: "completed" | "pending" - Trạng thái lộ trình
    - `completed`: Đã hoàn thành
    - `pending`: Đang thực hiện
  - `time`: Time - Thời gian (dùng cho báo cáo)
  - `createdAt`: Date - Thời điểm tạo bản ghi
  - `updatedAt`: Date - Thời điểm cập nhật cuối cùng

## Ví dụ
```json
{
  "_id": "RT001",
  "employee": {
    "_id": "NV001",
    "userId": "USER001",
    "name": "Nguyễn Văn A"
  },
  "companyId": "CTY001",
  "storeId": "CH001",
  "branchId": "CN001",
  "date": "2024-02-24",
  "totalDistance": 15000,
  "checkpoints": [
    {
      "time": "2024-02-24T08:00:00.000Z",
      "location": {
        "lat": 21.028511,
        "lng": 105.804817
      }
    },
    {
      "time": "2024-02-24T10:30:00.000Z",
      "location": {
        "lat": 21.025381,
        "lng": 105.801937
      }
    },
    {
      "time": "2024-02-24T14:00:00.000Z",
      "location": {
        "lat": 21.023067,
        "lng": 105.805799
      }
    },
    {
      "time": "2024-02-24T17:00:00.000Z",
      "location": {
        "lat": 21.028511,
        "lng": 105.804817
      }
    }
  ],
  "status": "completed",
  "time": {
    "date": 24,
    "month": 2,
    "year": 2024,
    "hours": 17,
    "minutes": 0
  },
  "createdAt": "2024-02-24T08:00:00.000Z",
  "updatedAt": "2024-02-24T17:00:00.000Z"
}
``` 