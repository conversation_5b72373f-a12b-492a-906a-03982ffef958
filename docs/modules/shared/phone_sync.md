# PhoneSync Plugin

Plugin **PhoneSync** là một công cụ độ<PERSON> lậ<PERSON>, đồng bộ dữ liệu từ thiết bị Android (call log và contacts) để phục vụ nhiều module trong hệ thống ERP như CRM và HRM.

## Chức năng chính

### 1. <PERSON><PERSON><PERSON> bộ Call Log
- **<PERSON><PERSON> tả**: <PERSON>hu thập và đồng bộ lịch sử cuộc gọi từ thiết bị Android để quản lý khách hàng hoặc nhân viên.  Chủ cửa hàng có thể thêm khách hàng từ danh sách cuộc gọi. Hoặc chủ công ty muốn quản lý sale thông qua danh sách, thời gian cuộc gọi.
- **Quy trình**:
  - L<PERSON>y dữ liệu cuộc gọi (số đi<PERSON> tho<PERSON>, th<PERSON><PERSON> gian, thờ<PERSON> lượ<PERSON>) từ ứng dụng Android.
  - <PERSON><PERSON><PERSON> dữ liệu qua API tới backend NestJS.
  - <PERSON><PERSON>u trữ vào database riêng (collection `phonesync_call_logs`).
  - <PERSON><PERSON> phối dữ liệu:
    - Đồng bộ vào CRM nếu liên quan đến khách hàng.
    - Đồng bộ vào HRM nếu liên quan đến nhân viên (dựa trên danh sách thiết bị công ty).

### 2. Đồng bộ Contacts
- **Mô tả**: Đồng bộ danh bạ từ thiết bị Android để quản lý thông tin khách hàng hoặc nhân viên.
- **Quy trình**:
  - Trích xuất danh bạ (tên, số điện thoại, email nếu có) từ ứng dụng Android.
  - Gửi dữ liệu qua API tới backend NestJS.
  - Lưu trữ vào database riêng (collection `phonesync_contacts`).
  - Cập nhật thông tin vào CRM (cho khách hàng) hoặc HRM (cho nhân viên) dựa trên ngữ cảnh.

## Ghi chú
- PhoneSync hoạt động độc lập, không gắn chặt vào một module cụ thể, cung cấp dữ liệu linh hoạt cho CRM, HRM, hoặc các module khác.
- Có thể mở rộng để hỗ trợ thêm tính năng như đồng bộ SMS hoặc phân tích dữ liệu giao tiếp (kết hợp với BI).