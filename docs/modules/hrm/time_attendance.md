Trong module **HRM** (Human Resource Management), **<PERSON><PERSON> thống chấm công** (Timekeeping System) là một tính năng quan trọng để quản lý thời gian làm việc của nhân viên. T<PERSON><PERSON> thuộc vào nhu cầu của doanh nghiệp, hệ thống chấm công có thể được sử dụng theo nhiều cách đa dạng. D<PERSON><PERSON><PERSON> đây, tôi sẽ phân tích các cách dùng khác nhau của "Hệ thống chấm công" trong HRM, kèm theo ngữ cảnh cụ thể, thông tin cần thiết và cách áp dụng để bạn hình dung rõ ràng. Điều này cũng hỗ trợ bạn thiết kế schema, interface hoặc docs phù hợp.

---

### Cá<PERSON> cách dùng đa dạng của "<PERSON><PERSON> thống chấm công" trong HRM

#### 1. <PERSON><PERSON><PERSON> công thủ công (Manual Timekeeping)
- **Mục đích:** Nhân viên tự ghi nhận thời gian vào/ra bằng cách nhập tay hoặc quản lý xác nhận.
- **Ngữ cảnh:** 
  - Doanh nghiệp nhỏ không có thiết bị chấm công tự động.
  - Nhân viên làm việc từ xa tự báo cáo giờ làm.
- **Thông tin cần thiết:**
  - ID nhân viên (`employeeId`).
  - Thời gian vào (`checkIn`).
  - Thời gian ra (`checkOut`).
  - Ghi chú (`note`) nếu cần giải thích (VD: lý do đi muộn).
- **Ví dụ ứng dụng:**
  - Nhân viên A ghi nhận check-in lúc 8:00 và check-out lúc 17:00 qua form online.
- **Interface cơ bản:**
  ```typescript
  export interface IManualTimeRecord {
    recordId: string;
    employeeId: string;
    checkIn: string; // ISO date string
    checkOut?: string; // Tùy chọn nếu chưa ra
    note?: string; // Ghi chú (VD: "Đi muộn vì tắc đường")
  }
  ```

#### 2. Chấm công tự động bằng máy (Device-based Timekeeping)
- **Mục đích:** Sử dụng thiết bị chấm công (máy quẹt thẻ, vân tay, khuôn mặt) để ghi nhận thời gian tự động.
- **Ngữ cảnh:**
  - Nhà máy, văn phòng có máy chấm công tại chỗ.
- **Thông tin cần thiết:**
  - ID thiết bị (`deviceId`).
  - Thời gian vào/ra (`checkIn`, `checkOut`).
  - Loại xác thực (`authType`: card, fingerprint, face).
- **Ví dụ ứng dụng:**
  - Nhân viên B quẹt vân tay lúc 7:55 vào và 17:05 ra tại máy chấm công ở chi nhánh X.
- **Interface:**
  ```typescript
  export interface IDeviceTimeRecord {
    recordId: string;
    employeeId: string;
    deviceId: string; // ID máy chấm công
    checkIn: string;
    checkOut?: string;
    authType: 'card' | 'fingerprint' | 'face';
  }
  ```

#### 3. Chấm công theo ca (Shift-based Timekeeping)
- **Mục đích:** Ghi nhận thời gian làm việc dựa trên ca được phân công trước.
- **Ngữ cảnh:**
  - Nhân viên làm việc theo lịch ca (sáng, chiều, đêm).
- **Thông tin cần thiết:**
  - ID ca (`shiftId`) liên kết với `/hrm/shift`.
  - Thời gian thực tế vào/ra (`actualCheckIn`, `actualCheckOut`).
  - Trạng thái (`status`: on-time, late, early-leave).
- **Ví dụ ứng dụng:**
  - Nhân viên C làm ca sáng (8:00-12:00), check-in 8:05 (muộn 5 phút), check-out 12:00.
- **Interface:**
  ```typescript
  export interface IShiftTimeRecord {
    recordId: string;
    employeeId: string;
    shiftId: string; // Liên kết với IFlexibleShift
    actualCheckIn: string;
    actualCheckOut?: string;
    status: 'on-time' | 'late' | 'early-leave' | 'absent';
  }
  ```

#### 4. Chấm công từ xa (Remote Timekeeping)
- **Mục đích:** Ghi nhận thời gian làm việc cho nhân viên không ở văn phòng (dựa trên app, GPS, hoặc web).
- **Ngữ cảnh:**
  - Nhân viên bán hàng di động hoặc làm việc tại nhà.
- **Thông tin cần thiết:**
  - Vị trí GPS (`location`: latitude, longitude).
  - Thời gian vào/ra (`checkIn`, `checkOut`).
  - Thiết bị sử dụng (`deviceInfo`).
- **Ví dụ ứng dụng:**
  - Nhân viên D check-in từ app lúc 9:00 tại tọa độ khách hàng, check-out lúc 16:00.
- **Interface:**
  ```typescript
  export interface IRemoteTimeRecord {
    recordId: string;
    employeeId: string;
    checkIn: string;
    checkOut?: string;
    location: {
      latitude: number;
      longitude: number;
    };
    deviceInfo?: string; // VD: "iPhone 14, App v1.2"
  }
  ```

#### 5. Chấm công theo dự án (Project-based Timekeeping)
- **Mục đích:** Theo dõi thời gian làm việc trên các dự án cụ thể mà nhân viên tham gia.
- **Ngữ cảnh:**
  - Nhân viên làm việc cho dự án mở chi nhánh mới.
- **Thông tin cần thiết:**
  - ID dự án (`projectId`) liên kết với `PMS`.
  - Thời gian bắt đầu/kết thúc công việc (`startTime`, `endTime`).
  - Công việc thực hiện (`taskId` hoặc mô tả).
- **Ví dụ ứng dụng:**
  - Nhân viên E làm 4 giờ cho dự án X, từ 9:00 đến 13:00.
- **Interface:**
  ```typescript
  export interface IProjectTimeRecord {
    recordId: string;
    employeeId: string;
    projectId: string; // Liên kết với PMS
    startTime: string;
    endTime?: string;
    taskId?: string; // Liên kết với ITask
  }
  ```

#### 6. Chấm công tích hợp KPI (KPI-linked Timekeeping)
- **Mục đích:** Ghi nhận thời gian làm việc để đánh giá hiệu suất (KPI) của nhân viên.
- **Ngữ cảnh:**
  - Số giờ làm việc ảnh hưởng đến KPI năng suất.
- **Thông tin cần thiết:**
  - ID KPI (`kpiId`) liên kết với `/hrm/kpi`.
  - Tổng giờ làm (`totalHours`).
  - Giá trị đóng góp (`contributionValue`).
- **Ví dụ ứng dụng:**
  - Nhân viên F làm 40 giờ/tuần, đóng góp 100% vào KPI "Thời gian làm việc".
- **Interface:**
  ```typescript
  export interface IKpiTimeRecord {
    recordId: string;
    employeeId: string;
    kpiId: string; // Liên kết với IKpi
    checkIn: string;
    checkOut?: string;
    totalHours?: number; // Tổng giờ tính từ checkIn/checkOut
    contributionValue?: number; // Đóng góp vào KPI
  }
  ```

#### 7. Chấm công theo tuyến đường (Route-based Timekeeping)
- **Mục đích:** Ghi nhận thời gian làm việc dựa trên tuyến đường di chuyển của nhân viên bán hàng.
- **Ngữ cảnh:**
  - Nhân viên bán hàng check-in/check-out tại các điểm khách hàng.
- **Thông tin cần thiết:**
  - ID tuyến đường (`routeId`) liên kết với `/hrm/sales-route`.
  - Danh sách điểm check-in/check-out (`checkPoints`).
- **Ví dụ ứng dụng:**
  - Nhân viên G check-in tại khách hàng A lúc 10:00, check-out tại khách hàng B lúc 15:00.
- **Interface:**
  ```typescript
  export interface IRouteTimeRecord {
    recordId: string;
    employeeId: string;
    routeId: string; // Liên kết với ISalesRoute
    checkPoints: {
      location: {
        latitude: number;
        longitude: number;
      };
      checkIn: string;
      checkOut?: string;
    }[];
  }
  ```
