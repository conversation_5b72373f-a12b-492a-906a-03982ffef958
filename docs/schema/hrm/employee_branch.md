# Employee Branch Schema

## Description
Lưu trữ thông tin chi tiết về vai trò, quyền hạn và điều kiện làm việc của nhân viên tại một chi nhánh cụ thể. <PERSON><PERSON> gồm thông tin về lương, ca làm việ<PERSON>, thiết bị chấm công và các quyền truy cập.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee_branch.interface.ts` (`IEmployeeBranch`)
- **Fields**:
  - `_id`: string - ID duy nhất của bản ghi
  - `employeeId`: string - ID liên kết với Employee
  - `positions`: string[] - <PERSON><PERSON><PERSON> vị trí công việc
  - `groups`: string[] - C<PERSON><PERSON> nhóm nhân viên
  - `roles`: string[] - <PERSON><PERSON><PERSON> vai trò
  - `permissions`: IAuthorizationRoleInterface['permissions'] - <PERSON><PERSON><PERSON><PERSON> hạn cụ thể
  - `isCompanyOwner`: boolean - <PERSON><PERSON> phải chủ công ty
  - `isStoreOwner`: boolean - Có phải chủ cửa hàng
  - `isStoreManager`: boolean - Có phải quản lý cửa hàng
  - `status`: 'active' | 'inactive' | 'deleted' - Trạng thái làm việc
  - `companyId`: string - ID công ty
  - `storeId`: string - ID cửa hàng
  - `branchId`: string - ID chi nhánh
  
  - `salary`: object - Thông tin lương
    - `baseSalary`: number - Lương cơ bản
    - `baseSalaryPerHour`: number - Lương theo giờ
    - `baseSalaryPerShift`: number - Lương theo ca
    - `bonuses`: Array - Danh sách thưởng
      - `type`: 'KPI' | 'night_shift' | 'attendance' | 'custom' - Loại thưởng
      - `amount`: number - Số tiền thưởng
      - `multiplier`: number (optional) - Hệ số nhân
    - `penalties`: Array - Danh sách phạt
      - `type`: 'late' | 'damage' | 'early_leave' | 'bad_review' | 'custom' - Loại phạt
      - `amount`: number - Số tiền phạt
      - `multiplier`: number (optional) - Hệ số nhân

  - `startWorkingDate`: Date - Ngày bắt đầu làm việc
  - `stopWorkingDate`: Date (optional) - Ngày kết thúc làm việc
  
  - `shiftSchedules`: Array - Lịch làm việc cố định
    - `shiftId`: string - ID ca làm việc
    - `day`: number[] - Các ngày trong tuần
    - `startDate`: Date - Ngày bắt đầu áp dụng
    - `endDate`: Date (optional) - Ngày kết thúc (null = vô thời hạn)
    - `status`: 'active' | 'inactive' - Trạng thái lịch

  - `checkinDevices`: string[] - Danh sách thiết bị được phép chấm công
  - `assignedWarehouses`: Array - Danh sách kho được phép truy cập
    - `_id`: string - ID kho
    - `name`: string - Tên kho
  
  - `deviceTokens`: DeviceTokenOnServer[] (optional) - Token thiết bị
  - `activeDeviceTokens`: DeviceTokenOnServer[] (optional) - Token thiết bị đang hoạt động

## Example
```json
{
  "_id": "EMP_BRANCH_001",
  "employeeId": "EMP001",
  "positions": ["cashier", "sales"],
  "groups": ["front_staff"],
  "roles": ["cashier", "inventory_viewer"],
  "permissions": {
    "pos": ["view", "create_order"],
    "inventory": ["view"]
  },
  "isCompanyOwner": false,
  "isStoreOwner": false,
  "isStoreManager": false,
  "status": "active",
  "companyId": "COMPANY001",
  "storeId": "STORE001",
  "branchId": "BRANCH001",
  
  "salary": {
    "baseSalary": 5000000,
    "baseSalaryPerHour": 25000,
    "baseSalaryPerShift": 200000,
    "bonuses": [
      {
        "type": "night_shift",
        "amount": 50000,
        "multiplier": 1.5
      },
      {
        "type": "attendance",
        "amount": 300000
      }
    ],
    "penalties": [
      {
        "type": "late",
        "amount": 50000,
        "multiplier": 1.2
      }
    ]
  },

  "startWorkingDate": "2024-01-01T00:00:00Z",
  "shiftSchedules": [
    {
      "shiftId": "SHIFT001",
      "day": [1, 2, 3, 4, 5],
      "startDate": "2024-01-01T00:00:00Z",
      "status": "active"
    }
  ],
  
  "checkinDevices": ["DEVICE001"],
  "assignedWarehouses": [
    {
      "_id": "WH001",
      "name": "Kho chính"
    }
  ]
}
```

## Notes
1. Tính năng chính:
   - Quản lý phân quyền chi tiết theo từng chi nhánh
   - Tính lương linh hoạt (theo giờ/ca/cố định)
   - Hệ thống thưởng/phạt tự động
   - Lịch làm việc có thể cố định hoặc linh động
   - Kiểm soát chấm công chặt chẽ qua thiết bị được duyệt

2. Các chế độ làm việc:
   - Lịch cố định: Tự động tạo ca theo `shiftSchedules`
   - Đăng ký thủ công: Nhân viên tự chọn ca
   - Phân công thủ công: Quản lý gán ca
   - Tự động phân công: Hệ thống tự cân bằng nhân lực

3. Quan hệ với các collection khác:
   - `employee`: Thông qua `employeeId`
   - `store_shifts`: Thông qua `shiftSchedules.shiftId`
   - `warehouses`: Thông qua `assignedWarehouses._id` 