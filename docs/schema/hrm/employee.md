# Employee Schema

## Description
Lưu trữ thông tin cơ bản của nhân viên trong hệ thống. Mỗi nhân viên có thể làm việc tại nhiều chi nh<PERSON>h khá<PERSON>hau, với thông tin chi tiết về vai trò và công việc tại từng chi nhánh được lưu trong bảng `employee_branch`.

## Interface
- **Ref**: `file://./../../../src/interfaces/hrm/employee.interface.ts` (`IEmployee`)
- **Fields**:
  - `_id`: string - ID duy nhất của nhân viên
  - `userId`: string (optional) - ID liên kết với tài khoản User (nếu có)
    - Không bắt buộc vì một số nhân viên (như lao động thời vụ) không cần tài khoản
    - <PERSON><PERSON><PERSON><PERSON> tr<PERSON>h tạo tài khoản User g<PERSON><PERSON> không cần thiết
  - `employeeBranchIds`: string[] - <PERSON><PERSON> sách ID các chi nhánh mà nhân viên làm việc
  - `name`: string - Tên đầy đủ của nhân viên
  - `profilePictures`: Array<{_id: string, url: string}> (optional) - Ảnh đại diện của nhân viên
  - `profileIdPictures`: Array<{_id: string, url: string}> (optional) - Ảnh CMND/CCCD của nhân viên

## Example
```json
{
  "_id": "EMP001",
  "userId": "USER123",  // Optional - chỉ có khi nhân viên cần tài khoản đăng nhập
  "employeeBranchIds": ["BRANCH001", "BRANCH002"],
  "name": "Nguyễn Văn A",
  "profilePictures": [
    {
      "_id": "PIC001",
      "url": "https://example.com/avatar.jpg"
    }
  ],
  "profileIdPictures": [
    {
      "_id": "ID001", 
      "url": "https://example.com/cmnd-front.jpg"
    },
    {
      "_id": "ID002",
      "url": "https://example.com/cmnd-back.jpg" 
    }
  ]
}
```

## Notes
1. Thiết kế này cho phép:
   - Quản lý nhân viên không cần tài khoản đăng nhập
   - Nhân viên có thể làm việc ở nhiều chi nhánh
   - Lưu trữ đầy đủ thông tin định danh (ảnh chân dung, CMND/CCCD)

2. Quan hệ với các collection khác:
   - `users`: Thông qua `userId` (optional)
   - `employee_branch`: Thông qua `employeeBranchIds`
